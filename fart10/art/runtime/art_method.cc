/*
 * Copyright (C) 2011 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "art_method.h"

#include <cstddef>

#include "android-base/stringprintf.h"

#include "arch/context.h"
#include "art_method-inl.h"
#include "class_linker-inl.h"
#include "class_root.h"
#include "debugger.h"
#include "dex/class_accessor-inl.h"
#include "dex/descriptors_names.h"
#include "dex/dex_file-inl.h"
#include "dex/dex_file_exception_helpers.h"
#include "dex/dex_instruction.h"
#include "dex/signature-inl.h"
#include "entrypoints/runtime_asm_entrypoints.h"
#include "gc/accounting/card_table-inl.h"
#include "hidden_api.h"
#include "interpreter/interpreter.h"
#include "jit/jit.h"
#include "jit/jit_code_cache.h"
#include "jit/profiling_info.h"
#include "jni/jni_internal.h"
#include "mirror/class-inl.h"
#include "mirror/class_ext-inl.h"
#include "mirror/executable.h"
#include "mirror/object-inl.h"
#include "mirror/object_array-inl.h"
#include "mirror/string.h"
#include "oat_file-inl.h"
#include "quicken_info.h"
#include "runtime_callbacks.h"
#include "scoped_thread_state_change-inl.h"
#include "vdex_file.h"

//add
#include <sys/syscall.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include "runtime.h"
#include <android/log.h>
#include <assert.h>
#include <errno.h>
#include <fcntl.h>
#include <pthread.h>
#include <stdarg.h>
#include <stddef.h>
#include <stdlib.h>
#include <string.h>
#include <sys/mman.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <sys/uio.h>
#include <sys/un.h>
#include <time.h>
#include <unistd.h>

//add
#define gettidv1() syscall(__NR_gettid)
#define LOG_TAG "ActivityThread"
#define ALOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)

namespace art {

    using android::base::StringPrintf;

    extern "C" void art_quick_invoke_stub(ArtMethod*, uint32_t*, uint32_t, Thread*, JValue*,
                                          const char*);
    extern "C" void art_quick_invoke_static_stub(ArtMethod*, uint32_t*, uint32_t, Thread*, JValue*,
                                                 const char*);

    //add
    uint8_t* getDexCodeItemEnd(const uint8_t **pData){
        uint32_t num_of_list = DecodeUnsignedLeb128(pData);
        for (;num_of_list>0;num_of_list--) {
            int32_t num_of_handlers=DecodeSignedLeb128(pData);
            int num=num_of_handlers;
            if (num_of_handlers<=0) {
                num=-num_of_handlers;
            }
            for (; num > 0; num--) {
                DecodeUnsignedLeb128(pData);
                DecodeUnsignedLeb128(pData);
            }
            if (num_of_handlers<=0) {
                DecodeUnsignedLeb128(pData);
            }
        }
        return (uint8_t*)(*pData);
    }

    //add
    extern "C" char *encodeBase64Buffer(char *str,long str_len,long* outlen){
        long len;
        char *res;
        int i,j;
        const char *base64_table="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

        if(str_len % 3 == 0)
            len=str_len/3*4;
        else
            len=(str_len/3+1)*4;

        res=(char*)malloc(sizeof(char)*(len+1));
        res[len]='\0';
        *outlen=len;

        for(i=0,j=0;i<len-2;j+=3,i+=4){
            res[i]=base64_table[str[j]>>2];
            res[i+1]=base64_table[(str[j]&0x3)<<4 | (str[j+1]>>4)];
            res[i+2]=base64_table[(str[j+1]&0xf)<<2 | (str[j+2]>>6)];
            res[i+3]=base64_table[str[j+2]&0x3f];
        }

        switch(str_len % 3){
            case 1:
                res[i-2]='=';
                res[i-1]='=';
                break;
            case 2:
                res[i-1]='=';
                break;
        }
        return res;
    }

    //add 创建目录
    bool ensure_dir_exists(const std::string& path) {
        int res = mkdir(path.c_str(), 0777);
        if (res == 0 || errno == EEXIST) {
            return true;
        } else {
            LOG(ERROR) << "mkdir failed: " << path << ", errno=" << errno << ", " << errno;
            return false;
        }
    }

    //add 跳过 Android 编译构建阶段的 dex2oatd 工具执行时的调用
    bool isValidAndroidApp(const char* procName) {
        // 排除 host 工具，例如 dex2oat、dex2oatd、aapt2 等
        return procName != nullptr &&
               strstr(procName, "/") == nullptr &&         // 不应该包含路径
               strstr(procName, "dex2oat") == nullptr &&   // 排除 dex2oat/dex2oatd
               strstr(procName, "soong") == nullptr;       // 排除构建系统相关路径
    }

    //add
    extern "C" void traceDexExecution(ArtMethod* artmethod) REQUIRES_SHARED(Locks::mutator_lock_) {
            char szCmdline[64] = {0};
            char szProcName[256] = {0};
            int procid = getpid();
            snprintf(szCmdline, sizeof(szCmdline), "/proc/%d/cmdline", procid);

            int fcmdline = open(szCmdline, O_RDONLY);
            if (fcmdline >= 0) {
                ssize_t result = read(fcmdline, szProcName, sizeof(szProcName) - 1);
                if (result < 0) {
                    LOG(ERROR) << "traceDexExecution: Failed to read cmdline";
                }
                close(fcmdline);
            } else {
                LOG(ERROR) << "[traceDexExecution] " << szCmdline << " open failed ";
            }

            if (szProcName[0] == '\0') {
                LOG(WARNING) << "[traceDexExecution] 获取进程名失败：" << artmethod->PrettyMethod();
                return;
            }

            if (!isValidAndroidApp(szProcName)) {
                LOG(WARNING) << "[traceDexExecution] 当前进程 " << szProcName << " 非法，跳过 dex dump";
                return;
            }

            const DexFile* dex_file = artmethod->GetDexFile();
            const uint8_t* begin_ = dex_file->Begin();
            size_t size_ = dex_file->Size();
            int size_int = static_cast<int>(size_);

            // 创建目录：/data/data/<packageName>/cyrus
            std::string base_dir = "/data/data/";
            std::string app_dir = base_dir + szProcName;
            std::string cyrus_dir = app_dir + "/cyrus";

            ensure_dir_exists(app_dir);
            ensure_dir_exists(cyrus_dir);

            // 保存 dex 文件
            std::string dex_path = cyrus_dir + "/" + std::to_string(size_int) + "_dex_file_execute.dex";
            // 保存 class 列表
            std::string classlist_path = cyrus_dir + "/" + std::to_string(size_int) + "_class_list_execute.txt";
            
            int dexfilefp = open(dex_path.c_str(), O_RDONLY);
            if (dexfilefp >= 0) {
                close(dexfilefp);
            } else {
                LOG(INFO) << "[traceDexExecution] " << artmethod->PrettyMethod() << " dump dex to " << dex_path;

                int fp = open(dex_path.c_str(), O_CREAT | O_APPEND | O_RDWR, 0666);
                if (fp >= 0) {
                    ssize_t w1 = write(fp, begin_, size_);
                    if (w1 < 0) {
                        LOG(ERROR) << "traceDexExecution: Failed to write dex file";
                    }
                    fsync(fp);
                    close(fp);

                    int class_list_file = open(classlist_path.c_str(), O_CREAT | O_APPEND | O_RDWR, 0666);
                    if (class_list_file >= 0) {
                        for (size_t ii = 0; ii < dex_file->NumClassDefs(); ++ii) {
                            const dex::ClassDef& class_def = dex_file->GetClassDef(ii);
                            const char* descriptor = dex_file->GetClassDescriptor(class_def);

                            ssize_t w2 = write(class_list_file, descriptor, strlen(descriptor));
                            if (w2 < 0) {
                                LOG(ERROR) << "traceDexExecution: Failed to write class descriptor";
                            }

                            ssize_t w3 = write(class_list_file, "\n", 1);
                            if (w3 < 0) {
                                LOG(ERROR) << "traceDexExecution: Failed to write newline";
                            }
                        }
                        fsync(class_list_file);
                        close(class_list_file);
                    } else {
                        LOG(ERROR) << "[traceDexExecution] " << class_list_file << " open failed, class_list_file=" << class_list_file;
                    }
                } else {
                    LOG(ERROR) << "[traceDexExecution] " << dex_path << " open failed, fp=" << fp;
                }
            }
    }

    //add
    extern "C" void traceMethodCode(ArtMethod* artmethod) REQUIRES_SHARED(Locks::mutator_lock_) {
            char szProcName[256] = {0};
            int procid = getpid();

            // 获取进程名
            char szCmdline[64] = {0};
            snprintf(szCmdline, sizeof(szCmdline), "/proc/%d/cmdline", procid);
            int fcmdline = open(szCmdline, O_RDONLY);
            if (fcmdline >= 0) {
                ssize_t result = read(fcmdline, szProcName, sizeof(szProcName) - 1);
                if (result < 0) {
                    LOG(ERROR) << "ArtMethod::traceMethodCode: read cmdline failed.";
                }
                close(fcmdline);
            } else {
                LOG(ERROR) << "[traceMethodCode] " << szCmdline << " open failed ";
            }

            if (szProcName[0] == '\0') {
                LOG(WARNING) << "[traceMethodCode] 获取进程名失败：" << artmethod->PrettyMethod();
                return;
            }

            if (!isValidAndroidApp(szProcName)) {
                LOG(WARNING) << "[traceMethodCode] 当前进程 " << szProcName << " 非法，跳过 dex dump";
                return;
            }

            const DexFile* dex_file = artmethod->GetDexFile();
            const uint8_t* begin_ = dex_file->Begin();
            size_t size_ = dex_file->Size();
            int size_int = static_cast<int>(size_);

            // 创建目录：/data/data/<packageName>/cyrus
            std::string base_dir = "/data/data/";
            std::string app_dir = base_dir + szProcName;
            std::string cyrus_dir = app_dir + "/cyrus";

            ensure_dir_exists(app_dir);
            ensure_dir_exists(cyrus_dir);

            // 保存 dex 文件
            std::string dex_path = cyrus_dir + "/" + std::to_string(size_int) + "_dex_file.dex";
            // 保存 class 列表
            std::string class_list_path = cyrus_dir + "/" + std::to_string(size_int) + "_class_list.txt";

            int dexfilefp = open(dex_path.c_str(), O_RDONLY);
            if (dexfilefp >= 0) {
                close(dexfilefp);
            } else {
                LOG(INFO) << "[traceMethodCode]" << artmethod->PrettyMethod() << " dump to " << dex_path;

                int fp = open(dex_path.c_str(), O_CREAT | O_APPEND | O_RDWR, 0666);
                if (fp >= 0) {
                    ssize_t w = write(fp, begin_, size_);
                    if (w < 0) {
                        LOG(ERROR) << "ArtMethod::traceMethodCode: write dexfile failed -> " << dex_path;
                    }
                    fsync(fp);
                    close(fp);
                    
                    int class_list_file = open(class_list_path.c_str(), O_CREAT | O_APPEND | O_RDWR, 0666);
                    if (class_list_file >= 0) {
                        for (size_t i = 0; i < dex_file->NumClassDefs(); ++i) {
                            const dex::ClassDef& class_def = dex_file->GetClassDef(i);
                            const char* descriptor = dex_file->GetClassDescriptor(class_def);

                            ssize_t w1 = write(class_list_file, descriptor, strlen(descriptor));
                            if (w1 < 0) {
                                LOG(ERROR) << "ArtMethod::traceMethodCode: write class descriptor failed";
                            }

                            ssize_t w2 = write(class_list_file, "\n", 1);
                            if (w2 < 0) {
                                LOG(ERROR) << "ArtMethod::traceMethodCode: write newline failed";
                            }
                        }
                        fsync(class_list_file);
                        close(class_list_file);
                    } else {
                        LOG(ERROR) << "[traceMethodCode] " << class_list_path << " open failed, class_list_file=" << class_list_file;
                    }
                } else {
                    LOG(ERROR) << "[traceMethodCode] " << dex_path << " open failed, fp=" << fp;
                }
            }

            // 保存指令码
            const dex::CodeItem* code_item = artmethod->GetCodeItem();
            if (LIKELY(code_item != nullptr)) {
                uint8_t* item = (uint8_t*)code_item;
                int code_item_len = 0;
                CodeItemDataAccessor accessor(*dex_file, code_item);
                if (accessor.TriesSize() > 0) {
                    const uint8_t* handler_data = accessor.GetCatchHandlerData();
                    uint8_t* tail = getDexCodeItemEnd(&handler_data);
                    code_item_len = static_cast<int>(tail - item);
                } else {
                    code_item_len = 16 + accessor.InsnsSizeInCodeUnits() * 2;
                }

                uint32_t method_idx = artmethod->GetDexMethodIndex();
                int offset = static_cast<int>(item - begin_);
                pid_t tid = gettidv1();
                // 保存 CodeItem
                std::string ins_path = cyrus_dir + "/" + std::to_string(size_int) + "_ins_" + std::to_string(tid) + ".bin";

                int fp2 = open(ins_path.c_str(), O_CREAT | O_APPEND | O_RDWR, 0666);
                if (fp2 >= 0) {
                    lseek(fp2, 0, SEEK_END);
                    std::string header = "{name:" + artmethod->PrettyMethod() +
                                         ",method_idx:" + std::to_string(method_idx) +
                                         ",offset:" + std::to_string(offset) +
                                         ",code_item_len:" + std::to_string(code_item_len) +
                                         ",ins:";

                    ssize_t w3 = write(fp2, header.c_str(), header.length());
                    if (w3 < 0) {
                        LOG(ERROR) << "ArtMethod::traceMethodCode: write header failed";
                    }

                    long outlen = 0;
                    char* base64result = encodeBase64Buffer((char*)item, (long)code_item_len, &outlen);
                    if (base64result != nullptr) {
                        ssize_t w4 = write(fp2, base64result, outlen);
                        if (w4 < 0) {
                            LOG(ERROR) << "ArtMethod::traceMethodCode: write base64 ins failed";
                        }
                        free(base64result);
                    }

                    ssize_t w5 = write(fp2, "};", 2);
                    if (w5 < 0) {
                        LOG(ERROR) << "ArtMethod::traceMethodCode: write tail failed";
                    }

                    fsync(fp2);
                    close(fp2);
                } else {
                    LOG(ERROR) << "[traceMethodCode] " << ins_path << " open failed, fp2=" << fp2;
                }
            }
    }

    //add
    extern "C" void callNativeMethodInspector(ArtMethod* artmethod)  REQUIRES_SHARED(Locks::mutator_lock_) {
            JValue *result=nullptr;
            Thread *self=nullptr;
            uint32_t temp=6;
            uint32_t* args=&temp;
            uint32_t args_size=6;
            artmethod->Invoke(self, args, args_size, result, "startCodeInspection");
    }

    ArtMethod *ArtMethod::FromReflectedMethod(const ScopedObjectAccessAlreadyRunnable &soa, jobject jlr_method) {
        ObjPtr<mirror::Executable> executable = soa.Decode<mirror::Executable>(jlr_method);
        DCHECK(executable != nullptr);
        return executable->GetArtMethod();
    }

    // Enforce that we he have the right index for runtime methods.
    static_assert(ArtMethod::kRuntimeMethodDexMethodIndex == dex::kDexNoIndex,
    "Wrong runtime-method dex method index");

    ArtMethod* ArtMethod::GetCanonicalMethod(PointerSize pointer_size) {
        if (LIKELY(!IsDefault())) {
            return this;
        } else {
            ObjPtr<mirror::Class> declaring_class = GetDeclaringClass();
            DCHECK(declaring_class->IsInterface());
            ArtMethod* ret = declaring_class->FindInterfaceMethod(GetDexCache(),
                                                                  GetDexMethodIndex(),
                                                                  pointer_size);
            DCHECK(ret != nullptr);
            return ret;
        }
    }

    ArtMethod* ArtMethod::GetNonObsoleteMethod() {
        if (LIKELY(!IsObsolete())) {
            return this;
        }
        DCHECK_EQ(kRuntimePointerSize, Runtime::Current()->GetClassLinker()->GetImagePointerSize());
        if (IsDirect()) {
            return &GetDeclaringClass()->GetDirectMethodsSlice(kRuntimePointerSize)[GetMethodIndex()];
        } else {
            return GetDeclaringClass()->GetVTableEntry(GetMethodIndex(), kRuntimePointerSize);
        }
    }

    ArtMethod* ArtMethod::GetSingleImplementation(PointerSize pointer_size) {
        if (!IsAbstract()) {
            // A non-abstract's single implementation is itself.
            return this;
        }
        return reinterpret_cast<ArtMethod*>(GetDataPtrSize(pointer_size));
    }

    ObjPtr<mirror::DexCache> ArtMethod::GetObsoleteDexCache() {
        DCHECK(!Runtime::Current()->IsAotCompiler()) << PrettyMethod();
        DCHECK(IsObsolete());
        ObjPtr<mirror::ClassExt> ext(GetDeclaringClass()->GetExtData());
        CHECK(!ext.IsNull());
        ObjPtr<mirror::PointerArray> obsolete_methods(ext->GetObsoleteMethods());
        CHECK(!obsolete_methods.IsNull());
        DCHECK(ext->GetObsoleteDexCaches() != nullptr);
        int32_t len = obsolete_methods->GetLength();
        DCHECK_EQ(len, ext->GetObsoleteDexCaches()->GetLength());
        // Using kRuntimePointerSize (instead of using the image's pointer size) is fine since images
        // should never have obsolete methods in them so they should always be the same.
        PointerSize pointer_size = kRuntimePointerSize;
        DCHECK_EQ(kRuntimePointerSize, Runtime::Current()->GetClassLinker()->GetImagePointerSize());
        for (int32_t i = 0; i < len; i++) {
            if (this == obsolete_methods->GetElementPtrSize<ArtMethod*>(i, pointer_size)) {
                return ext->GetObsoleteDexCaches()->Get(i);
            }
        }
        LOG(FATAL) << "This method does not appear in the obsolete map of its class!";
        UNREACHABLE();
    }

    uint16_t ArtMethod::FindObsoleteDexClassDefIndex() {
        DCHECK(!Runtime::Current()->IsAotCompiler()) << PrettyMethod();
        DCHECK(IsObsolete());
        const DexFile* dex_file = GetDexFile();
        const dex::TypeIndex declaring_class_type = dex_file->GetMethodId(GetDexMethodIndex()).class_idx_;
        const dex::ClassDef* class_def = dex_file->FindClassDef(declaring_class_type);
        CHECK(class_def != nullptr);
        return dex_file->GetIndexForClassDef(*class_def);
    }

    void ArtMethod::ThrowInvocationTimeError() {
        DCHECK(!IsInvokable());
        // NOTE: IsDefaultConflicting must be first since the actual method might or might not be abstract
        //       due to the way we select it.
        if (IsDefaultConflicting()) {
            ThrowIncompatibleClassChangeErrorForMethodConflict(this);
        } else {
            DCHECK(IsAbstract());
            ThrowAbstractMethodError(this);
        }
    }

    InvokeType ArtMethod::GetInvokeType() {
        // TODO: kSuper?
        if (IsStatic()) {
            return kStatic;
        } else if (GetDeclaringClass()->IsInterface()) {
            return kInterface;
        } else if (IsDirect()) {
            return kDirect;
        } else if (IsPolymorphicSignature()) {
            return kPolymorphic;
        } else {
            return kVirtual;
        }
    }

    size_t ArtMethod::NumArgRegisters(const char* shorty) {
        CHECK_NE(shorty[0], '\0');
        uint32_t num_registers = 0;
        for (const char* s = shorty + 1; *s != '\0'; ++s) {
            if (*s == 'D' || *s == 'J') {
                num_registers += 2;
            } else {
                num_registers += 1;
            }
        }
        return num_registers;
    }

    bool ArtMethod::HasSameNameAndSignature(ArtMethod* other) {
        ScopedAssertNoThreadSuspension ants("HasSameNameAndSignature");
        const DexFile* dex_file = GetDexFile();
        const dex::MethodId& mid = dex_file->GetMethodId(GetDexMethodIndex());
        if (GetDexCache() == other->GetDexCache()) {
            const dex::MethodId& mid2 = dex_file->GetMethodId(other->GetDexMethodIndex());
            return mid.name_idx_ == mid2.name_idx_ && mid.proto_idx_ == mid2.proto_idx_;
        }
        const DexFile* dex_file2 = other->GetDexFile();
        const dex::MethodId& mid2 = dex_file2->GetMethodId(other->GetDexMethodIndex());
        if (!DexFile::StringEquals(dex_file, mid.name_idx_, dex_file2, mid2.name_idx_)) {
            return false;  // Name mismatch.
        }
        return dex_file->GetMethodSignature(mid) == dex_file2->GetMethodSignature(mid2);
    }

    ArtMethod* ArtMethod::FindOverriddenMethod(PointerSize pointer_size) {
        if (IsStatic()) {
            return nullptr;
        }
        ObjPtr<mirror::Class> declaring_class = GetDeclaringClass();
        ObjPtr<mirror::Class> super_class = declaring_class->GetSuperClass();
        uint16_t method_index = GetMethodIndex();
        ArtMethod* result = nullptr;
        // Did this method override a super class method? If so load the result from the super class'
        // vtable
        if (super_class->HasVTable() && method_index < super_class->GetVTableLength()) {
            result = super_class->GetVTableEntry(method_index, pointer_size);
        } else {
            // Method didn't override superclass method so search interfaces
            if (IsProxyMethod()) {
                result = GetInterfaceMethodIfProxy(pointer_size);
                DCHECK(result != nullptr);
            } else {
                ObjPtr<mirror::IfTable> iftable = GetDeclaringClass()->GetIfTable();
                for (size_t i = 0; i < iftable->Count() && result == nullptr; i++) {
                    ObjPtr<mirror::Class> interface = iftable->GetInterface(i);
                    for (ArtMethod& interface_method : interface->GetVirtualMethods(pointer_size)) {
                        if (HasSameNameAndSignature(interface_method.GetInterfaceMethodIfProxy(pointer_size))) {
                            result = &interface_method;
                            break;
                        }
                    }
                }
            }
        }
        DCHECK(result == nullptr ||
               GetInterfaceMethodIfProxy(pointer_size)->HasSameNameAndSignature(
                       result->GetInterfaceMethodIfProxy(pointer_size)));
        return result;
    }

    uint32_t ArtMethod::FindDexMethodIndexInOtherDexFile(const DexFile& other_dexfile,
                                                         uint32_t name_and_signature_idx) {
        const DexFile* dexfile = GetDexFile();
        const uint32_t dex_method_idx = GetDexMethodIndex();
        const dex::MethodId& mid = dexfile->GetMethodId(dex_method_idx);
        const dex::MethodId& name_and_sig_mid = other_dexfile.GetMethodId(name_and_signature_idx);
        DCHECK_STREQ(dexfile->GetMethodName(mid), other_dexfile.GetMethodName(name_and_sig_mid));
        DCHECK_EQ(dexfile->GetMethodSignature(mid), other_dexfile.GetMethodSignature(name_and_sig_mid));
        if (dexfile == &other_dexfile) {
            return dex_method_idx;
        }
        const char* mid_declaring_class_descriptor = dexfile->StringByTypeIdx(mid.class_idx_);
        const dex::TypeId* other_type_id = other_dexfile.FindTypeId(mid_declaring_class_descriptor);
        if (other_type_id != nullptr) {
            const dex::MethodId* other_mid = other_dexfile.FindMethodId(
                    *other_type_id, other_dexfile.GetStringId(name_and_sig_mid.name_idx_),
                    other_dexfile.GetProtoId(name_and_sig_mid.proto_idx_));
            if (other_mid != nullptr) {
                return other_dexfile.GetIndexForMethodId(*other_mid);
            }
        }
        return dex::kDexNoIndex;
    }

    uint32_t ArtMethod::FindCatchBlock(Handle<mirror::Class> exception_type,
                                       uint32_t dex_pc, bool* has_no_move_exception) {
        // Set aside the exception while we resolve its type.
        Thread* self = Thread::Current();
        StackHandleScope<1> hs(self);
        Handle<mirror::Throwable> exception(hs.NewHandle(self->GetException()));
        self->ClearException();
        // Default to handler not found.
        uint32_t found_dex_pc = dex::kDexNoIndex;
        // Iterate over the catch handlers associated with dex_pc.
        CodeItemDataAccessor accessor(DexInstructionData());
        for (CatchHandlerIterator it(accessor, dex_pc); it.HasNext(); it.Next()) {
            dex::TypeIndex iter_type_idx = it.GetHandlerTypeIndex();
            // Catch all case
            if (!iter_type_idx.IsValid()) {
                found_dex_pc = it.GetHandlerAddress();
                break;
            }
            // Does this catch exception type apply?
            ObjPtr<mirror::Class> iter_exception_type = ResolveClassFromTypeIndex(iter_type_idx);
            if (UNLIKELY(iter_exception_type == nullptr)) {
                // Now have a NoClassDefFoundError as exception. Ignore in case the exception class was
                // removed by a pro-guard like tool.
                // Note: this is not RI behavior. RI would have failed when loading the class.
                self->ClearException();
                // Delete any long jump context as this routine is called during a stack walk which will
                // release its in use context at the end.
                delete self->GetLongJumpContext();
                LOG(WARNING) << "Unresolved exception class when finding catch block: "
                             << DescriptorToDot(GetTypeDescriptorFromTypeIdx(iter_type_idx));
            } else if (iter_exception_type->IsAssignableFrom(exception_type.Get())) {
                found_dex_pc = it.GetHandlerAddress();
                break;
            }
        }
        if (found_dex_pc != dex::kDexNoIndex) {
            const Instruction& first_catch_instr = accessor.InstructionAt(found_dex_pc);
            *has_no_move_exception = (first_catch_instr.Opcode() != Instruction::MOVE_EXCEPTION);
        }
        // Put the exception back.
        if (exception != nullptr) {
            self->SetException(exception.Get());
        }
        return found_dex_pc;
    }

    void ArtMethod::Invoke(Thread* self, uint32_t* args, uint32_t args_size, JValue* result,
                           const char* shorty) {
        //add
        if (self == nullptr) {
            traceMethodCode(this);
            return;
        }

        if (UNLIKELY(__builtin_frame_address(0) < self->GetStackEnd())) {
            ThrowStackOverflowError(self);
            return;
        }

        if (kIsDebugBuild) {
            self->AssertThreadSuspensionIsAllowable();
            CHECK_EQ(kRunnable, self->GetState());
            CHECK_STREQ(GetInterfaceMethodIfProxy(kRuntimePointerSize)->GetShorty(), shorty);
        }

        // Push a transition back into managed code onto the linked list in thread.
        ManagedStack fragment;
        self->PushManagedStackFragment(&fragment);

        Runtime* runtime = Runtime::Current();
        // Call the invoke stub, passing everything as arguments.
        // If the runtime is not yet started or it is required by the debugger, then perform the
        // Invocation by the interpreter, explicitly forcing interpretation over JIT to prevent
        // cycling around the various JIT/Interpreter methods that handle method invocation.
        if (UNLIKELY(!runtime->IsStarted() ||
                     (self->IsForceInterpreter() && !IsNative() && !IsProxyMethod() && IsInvokable()) ||
                     Dbg::IsForcedInterpreterNeededForCalling(self, this))) {
            if (IsStatic()) {
                art::interpreter::EnterInterpreterFromInvoke(
                        self, this, nullptr, args, result, /*stay_in_interpreter=*/ true);
            } else {
                mirror::Object* receiver =
                        reinterpret_cast<StackReference<mirror::Object>*>(&args[0])->AsMirrorPtr();
                art::interpreter::EnterInterpreterFromInvoke(
                        self, this, receiver, args + 1, result, /*stay_in_interpreter=*/ true);
            }
        } else {
            DCHECK_EQ(runtime->GetClassLinker()->GetImagePointerSize(), kRuntimePointerSize);

            constexpr bool kLogInvocationStartAndReturn = false;
            bool have_quick_code = GetEntryPointFromQuickCompiledCode() != nullptr;
            if (LIKELY(have_quick_code)) {
                if (kLogInvocationStartAndReturn) {
                    LOG(INFO) << StringPrintf(
                            "Invoking '%s' quick code=%p static=%d", PrettyMethod().c_str(),
                            GetEntryPointFromQuickCompiledCode(), static_cast<int>(IsStatic() ? 1 : 0));
                }

                // Ensure that we won't be accidentally calling quick compiled code when -Xint.
                if (kIsDebugBuild && runtime->GetInstrumentation()->IsForcedInterpretOnly()) {
                    CHECK(!runtime->UseJitCompilation());
                    const void* oat_quick_code =
                            (IsNative() || !IsInvokable() || IsProxyMethod() || IsObsolete())
                            ? nullptr
                            : GetOatMethodQuickCode(runtime->GetClassLinker()->GetImagePointerSize());
                    CHECK(oat_quick_code == nullptr || oat_quick_code != GetEntryPointFromQuickCompiledCode())
                            << "Don't call compiled code when -Xint " << PrettyMethod();
                }

                if (!IsStatic()) {
                    (*art_quick_invoke_stub)(this, args, args_size, self, result, shorty);
                } else {
                    (*art_quick_invoke_static_stub)(this, args, args_size, self, result, shorty);
                }
                if (UNLIKELY(self->GetException() == Thread::GetDeoptimizationException())) {
                    // Unusual case where we were running generated code and an
                    // exception was thrown to force the activations to be removed from the
                    // stack. Continue execution in the interpreter.
                    self->DeoptimizeWithDeoptimizationException(result);
                }
                if (kLogInvocationStartAndReturn) {
                    LOG(INFO) << StringPrintf("Returned '%s' quick code=%p", PrettyMethod().c_str(),
                                              GetEntryPointFromQuickCompiledCode());
                }
            } else {
                LOG(INFO) << "Not invoking '" << PrettyMethod() << "' code=null";
                if (result != nullptr) {
                    result->SetJ(0);
                }
            }
        }

        // Pop transition.
        self->PopManagedStackFragment(fragment);
    }

    const void* ArtMethod::RegisterNative(const void* native_method) {
        CHECK(IsNative()) << PrettyMethod();
        CHECK(native_method != nullptr) << PrettyMethod();
        void* new_native_method = nullptr;
        Runtime::Current()->GetRuntimeCallbacks()->RegisterNativeMethod(this,
                                                                        native_method,
                /*out*/&new_native_method);
        SetEntryPointFromJni(new_native_method);
        return new_native_method;
    }

    void ArtMethod::UnregisterNative() {
        CHECK(IsNative()) << PrettyMethod();
        // restore stub to lookup native pointer via dlsym
        SetEntryPointFromJni(GetJniDlsymLookupStub());
    }

    bool ArtMethod::IsOverridableByDefaultMethod() {
        return GetDeclaringClass()->IsInterface();
    }

    bool ArtMethod::IsPolymorphicSignature() {
        // Methods with a polymorphic signature have constraints that they
        // are native and varargs and belong to either MethodHandle or VarHandle.
        if (!IsNative() || !IsVarargs()) {
            return false;
        }
        ObjPtr<mirror::ObjectArray<mirror::Class>> class_roots =
                Runtime::Current()->GetClassLinker()->GetClassRoots();
        ObjPtr<mirror::Class> cls = GetDeclaringClass();
        return (cls == GetClassRoot<mirror::MethodHandle>(class_roots) ||
                cls == GetClassRoot<mirror::VarHandle>(class_roots));
    }

    static uint32_t GetOatMethodIndexFromMethodIndex(const DexFile& dex_file,
                                                     uint16_t class_def_idx,
                                                     uint32_t method_idx) {
        ClassAccessor accessor(dex_file, class_def_idx);
        uint32_t class_def_method_index = 0u;
        for (const ClassAccessor::Method& method : accessor.GetMethods()) {
            if (method.GetIndex() == method_idx) {
                return class_def_method_index;
            }
            class_def_method_index++;
        }
        LOG(FATAL) << "Failed to find method index " << method_idx << " in " << dex_file.GetLocation();
        UNREACHABLE();
    }

// We use the method's DexFile and declaring class name to find the OatMethod for an obsolete
// method.  This is extremely slow but we need it if we want to be able to have obsolete native
// methods since we need this to find the size of its stack frames.
//
// NB We could (potentially) do this differently and rely on the way the transformation is applied
// in order to use the entrypoint to find this information. However, for debugging reasons (most
// notably making sure that new invokes of obsolete methods fail) we choose to instead get the data
// directly from the dex file.
    static const OatFile::OatMethod FindOatMethodFromDexFileFor(ArtMethod* method, bool* found)
    REQUIRES_SHARED(Locks::mutator_lock_) {
            DCHECK(method->IsObsolete() && method->IsNative());
            const DexFile* dex_file = method->GetDexFile();

            // recreate the class_def_index from the descriptor.
            std::string descriptor_storage;
            const dex::TypeId* declaring_class_type_id =
            dex_file->FindTypeId(method->GetDeclaringClass()->GetDescriptor(&descriptor_storage));
            CHECK(declaring_class_type_id != nullptr);
            dex::TypeIndex declaring_class_type_index = dex_file->GetIndexForTypeId(*declaring_class_type_id);
            const dex::ClassDef* declaring_class_type_def =
            dex_file->FindClassDef(declaring_class_type_index);
            CHECK(declaring_class_type_def != nullptr);
            uint16_t declaring_class_def_index = dex_file->GetIndexForClassDef(*declaring_class_type_def);

            size_t oat_method_index = GetOatMethodIndexFromMethodIndex(*dex_file,
            declaring_class_def_index,
            method->GetDexMethodIndex());

            OatFile::OatClass oat_class = OatFile::FindOatClass(*dex_file,
            declaring_class_def_index,
            found);
            if (!(*found)) {
                return OatFile::OatMethod::Invalid();
            }
            return oat_class.GetOatMethod(oat_method_index);
    }

    static const OatFile::OatMethod FindOatMethodFor(ArtMethod* method,
                                                     PointerSize pointer_size,
                                                     bool* found)
    REQUIRES_SHARED(Locks::mutator_lock_) {
            if (UNLIKELY(method->IsObsolete())) {
                // We shouldn't be calling this with obsolete methods except for native obsolete methods for
                // which we need to use the oat method to figure out how large the quick frame is.
                DCHECK(method->IsNative()) << "We should only be finding the OatMethod of obsolete methods in "
                                           << "order to allow stack walking. Other obsolete methods should "
                                           << "never need to access this information.";
                DCHECK_EQ(pointer_size, kRuntimePointerSize) << "Obsolete method in compiler!";
                return FindOatMethodFromDexFileFor(method, found);
            }
            // Although we overwrite the trampoline of non-static methods, we may get here via the resolution
            // method for direct methods (or virtual methods made direct).
            ObjPtr<mirror::Class> declaring_class = method->GetDeclaringClass();
            size_t oat_method_index;
            if (method->IsStatic() || method->IsDirect()) {
                // Simple case where the oat method index was stashed at load time.
                oat_method_index = method->GetMethodIndex();
            } else {
                // Compute the oat_method_index by search for its position in the declared virtual methods.
                oat_method_index = declaring_class->NumDirectMethods();
                bool found_virtual = false;
                for (ArtMethod& art_method : declaring_class->GetVirtualMethods(pointer_size)) {
                    // Check method index instead of identity in case of duplicate method definitions.
                    if (method->GetDexMethodIndex() == art_method.GetDexMethodIndex()) {
                        found_virtual = true;
                        break;
                    }
                    oat_method_index++;
                }
                CHECK(found_virtual) << "Didn't find oat method index for virtual method: "
                                     << method->PrettyMethod();
            }
            DCHECK_EQ(oat_method_index,
            GetOatMethodIndexFromMethodIndex(declaring_class->GetDexFile(),
            method->GetDeclaringClass()->GetDexClassDefIndex(),
            method->GetDexMethodIndex()));
            OatFile::OatClass oat_class = OatFile::FindOatClass(declaring_class->GetDexFile(),
            declaring_class->GetDexClassDefIndex(),
            found);
            if (!(*found)) {
                return OatFile::OatMethod::Invalid();
            }
            return oat_class.GetOatMethod(oat_method_index);
    }

    bool ArtMethod::EqualParameters(Handle<mirror::ObjectArray<mirror::Class>> params) {
        const DexFile* dex_file = GetDexFile();
        const auto& method_id = dex_file->GetMethodId(GetDexMethodIndex());
        const auto& proto_id = dex_file->GetMethodPrototype(method_id);
        const dex::TypeList* proto_params = dex_file->GetProtoParameters(proto_id);
        auto count = proto_params != nullptr ? proto_params->Size() : 0u;
        auto param_len = params != nullptr ? params->GetLength() : 0u;
        if (param_len != count) {
            return false;
        }
        auto* cl = Runtime::Current()->GetClassLinker();
        for (size_t i = 0; i < count; ++i) {
            dex::TypeIndex type_idx = proto_params->GetTypeItem(i).type_idx_;
            ObjPtr<mirror::Class> type = cl->ResolveType(type_idx, this);
            if (type == nullptr) {
                Thread::Current()->AssertPendingException();
                return false;
            }
            if (type != params->GetWithoutChecks(i)) {
                return false;
            }
        }
        return true;
    }

    ArrayRef<const uint8_t> ArtMethod::GetQuickenedInfo() {
        const DexFile& dex_file = *GetDexFile();
        const OatDexFile* oat_dex_file = dex_file.GetOatDexFile();
        if (oat_dex_file == nullptr) {
            return ArrayRef<const uint8_t>();
        }
        return oat_dex_file->GetQuickenedInfoOf(dex_file, GetDexMethodIndex());
    }

    uint16_t ArtMethod::GetIndexFromQuickening(uint32_t dex_pc) {
        ArrayRef<const uint8_t> data = GetQuickenedInfo();
        if (data.empty()) {
            return DexFile::kDexNoIndex16;
        }
        QuickenInfoTable table(data);
        uint32_t quicken_index = 0;
        for (const DexInstructionPcPair& pair : DexInstructions()) {
            if (pair.DexPc() == dex_pc) {
                return table.GetData(quicken_index);
            }
            if (QuickenInfoTable::NeedsIndexForInstruction(&pair.Inst())) {
                ++quicken_index;
            }
        }
        return DexFile::kDexNoIndex16;
    }

    const OatQuickMethodHeader* ArtMethod::GetOatQuickMethodHeader(uintptr_t pc) {
        // Our callers should make sure they don't pass the instrumentation exit pc,
        // as this method does not look at the side instrumentation stack.
        DCHECK_NE(pc, reinterpret_cast<uintptr_t>(GetQuickInstrumentationExitPc()));

        if (IsRuntimeMethod()) {
            return nullptr;
        }

        Runtime* runtime = Runtime::Current();
        const void* existing_entry_point = GetEntryPointFromQuickCompiledCode();
        CHECK(existing_entry_point != nullptr) << PrettyMethod() << "@" << this;
        ClassLinker* class_linker = runtime->GetClassLinker();

        if (existing_entry_point == GetQuickProxyInvokeHandler()) {
            DCHECK(IsProxyMethod() && !IsConstructor());
            // The proxy entry point does not have any method header.
            return nullptr;
        }

        // Check whether the current entry point contains this pc.
        if (!class_linker->IsQuickGenericJniStub(existing_entry_point) &&
            !class_linker->IsQuickResolutionStub(existing_entry_point) &&
            !class_linker->IsQuickToInterpreterBridge(existing_entry_point) &&
            existing_entry_point != GetQuickInstrumentationEntryPoint()) {
            OatQuickMethodHeader* method_header =
                    OatQuickMethodHeader::FromEntryPoint(existing_entry_point);

            if (method_header->Contains(pc)) {
                return method_header;
            }
        }

        // Check whether the pc is in the JIT code cache.
        jit::Jit* jit = runtime->GetJit();
        if (jit != nullptr) {
            jit::JitCodeCache* code_cache = jit->GetCodeCache();
            OatQuickMethodHeader* method_header = code_cache->LookupMethodHeader(pc, this);
            if (method_header != nullptr) {
                DCHECK(method_header->Contains(pc));
                return method_header;
            } else {
                DCHECK(!code_cache->ContainsPc(reinterpret_cast<const void*>(pc)))
                        << PrettyMethod()
                        << ", pc=" << std::hex << pc
                        << ", entry_point=" << std::hex << reinterpret_cast<uintptr_t>(existing_entry_point)
                        << ", copy=" << std::boolalpha << IsCopied()
                        << ", proxy=" << std::boolalpha << IsProxyMethod();
            }
        }

        // The code has to be in an oat file.
        bool found;
        OatFile::OatMethod oat_method =
                FindOatMethodFor(this, class_linker->GetImagePointerSize(), &found);
        if (!found) {
            if (IsNative()) {
                // We are running the GenericJNI stub. The entrypoint may point
                // to different entrypoints or to a JIT-compiled JNI stub.
                DCHECK(class_linker->IsQuickGenericJniStub(existing_entry_point) ||
                       class_linker->IsQuickResolutionStub(existing_entry_point) ||
                       existing_entry_point == GetQuickInstrumentationEntryPoint() ||
                       (jit != nullptr && jit->GetCodeCache()->ContainsPc(existing_entry_point)));
                return nullptr;
            }
            // Only for unit tests.
            // TODO(ngeoffray): Update these tests to pass the right pc?
            return OatQuickMethodHeader::FromEntryPoint(existing_entry_point);
        }
        const void* oat_entry_point = oat_method.GetQuickCode();
        if (oat_entry_point == nullptr || class_linker->IsQuickGenericJniStub(oat_entry_point)) {
            DCHECK(IsNative()) << PrettyMethod();
            return nullptr;
        }

        OatQuickMethodHeader* method_header = OatQuickMethodHeader::FromEntryPoint(oat_entry_point);
        if (pc == 0) {
            // This is a downcall, it can only happen for a native method.
            DCHECK(IsNative());
            return method_header;
        }

        DCHECK(method_header->Contains(pc))
                << PrettyMethod()
                << " " << std::hex << pc << " " << oat_entry_point
                << " " << (uintptr_t)(method_header->GetCode() + method_header->GetCodeSize());
        return method_header;
    }

    const void* ArtMethod::GetOatMethodQuickCode(PointerSize pointer_size) {
        bool found;
        OatFile::OatMethod oat_method = FindOatMethodFor(this, pointer_size, &found);
        if (found) {
            return oat_method.GetQuickCode();
        }
        return nullptr;
    }

    bool ArtMethod::HasAnyCompiledCode() {
        if (IsNative() || !IsInvokable() || IsProxyMethod()) {
            return false;
        }

        // Check whether the JIT has compiled it.
        Runtime* runtime = Runtime::Current();
        jit::Jit* jit = runtime->GetJit();
        if (jit != nullptr && jit->GetCodeCache()->ContainsMethod(this)) {
            return true;
        }

        // Check whether we have AOT code.
        return GetOatMethodQuickCode(runtime->GetClassLinker()->GetImagePointerSize()) != nullptr;
    }

    void ArtMethod::SetIntrinsic(uint32_t intrinsic) {
        // Currently we only do intrinsics for static/final methods or methods of final
        // classes. We don't set kHasSingleImplementation for those methods.
        DCHECK(IsStatic() || IsFinal() || GetDeclaringClass()->IsFinal()) <<
                                                                          "Potential conflict with kAccSingleImplementation";
        static const int kAccFlagsShift = CTZ(kAccIntrinsicBits);
        DCHECK_LE(intrinsic, kAccIntrinsicBits >> kAccFlagsShift);
        uint32_t intrinsic_bits = intrinsic << kAccFlagsShift;
        uint32_t new_value = (GetAccessFlags() & ~kAccIntrinsicBits) | kAccIntrinsic | intrinsic_bits;
        if (kIsDebugBuild) {
            uint32_t java_flags = (GetAccessFlags() & kAccJavaFlagsMask);
            bool is_constructor = IsConstructor();
            bool is_synchronized = IsSynchronized();
            bool skip_access_checks = SkipAccessChecks();
            bool is_fast_native = IsFastNative();
            bool is_critical_native = IsCriticalNative();
            bool is_copied = IsCopied();
            bool is_miranda = IsMiranda();
            bool is_default = IsDefault();
            bool is_default_conflict = IsDefaultConflicting();
            bool is_compilable = IsCompilable();
            bool must_count_locks = MustCountLocks();
            // Recompute flags instead of getting them from the current access flags because
            // access flags may have been changed to deduplicate warning messages (b/129063331).
            uint32_t hiddenapi_flags = hiddenapi::CreateRuntimeFlags(this);
            SetAccessFlags(new_value);
            DCHECK_EQ(java_flags, (GetAccessFlags() & kAccJavaFlagsMask));
            DCHECK_EQ(is_constructor, IsConstructor());
            DCHECK_EQ(is_synchronized, IsSynchronized());
            DCHECK_EQ(skip_access_checks, SkipAccessChecks());
            DCHECK_EQ(is_fast_native, IsFastNative());
            DCHECK_EQ(is_critical_native, IsCriticalNative());
            DCHECK_EQ(is_copied, IsCopied());
            DCHECK_EQ(is_miranda, IsMiranda());
            DCHECK_EQ(is_default, IsDefault());
            DCHECK_EQ(is_default_conflict, IsDefaultConflicting());
            DCHECK_EQ(is_compilable, IsCompilable());
            DCHECK_EQ(must_count_locks, MustCountLocks());
            // Only DCHECK that we have preserved the hidden API access flags if the
            // original method was not on the whitelist. This is because the core image
            // does not have the access flags set (b/77733081).
            if ((hiddenapi_flags & kAccHiddenapiBits) != kAccPublicApi) {
                DCHECK_EQ(hiddenapi_flags, hiddenapi::GetRuntimeFlags(this)) << PrettyMethod();
            }
        } else {
            SetAccessFlags(new_value);
        }
    }

    void ArtMethod::SetNotIntrinsic() {
        if (!IsIntrinsic()) {
            return;
        }

        // Read the existing hiddenapi flags.
        uint32_t hiddenapi_runtime_flags = hiddenapi::GetRuntimeFlags(this);

        // Clear intrinsic-related access flags.
        ClearAccessFlags(kAccIntrinsic | kAccIntrinsicBits);

        // Re-apply hidden API access flags now that the method is not an intrinsic.
        SetAccessFlags(GetAccessFlags() | hiddenapi_runtime_flags);
        DCHECK_EQ(hiddenapi_runtime_flags, hiddenapi::GetRuntimeFlags(this));
    }

    void ArtMethod::CopyFrom(ArtMethod* src, PointerSize image_pointer_size) {
        memcpy(reinterpret_cast<void*>(this), reinterpret_cast<const void*>(src),
               Size(image_pointer_size));
        declaring_class_ = GcRoot<mirror::Class>(const_cast<ArtMethod*>(src)->GetDeclaringClass());

        // If the entry point of the method we are copying from is from JIT code, we just
        // put the entry point of the new method to interpreter or GenericJNI. We could set
        // the entry point to the JIT code, but this would require taking the JIT code cache
        // lock to notify it, which we do not want at this level.
        Runtime* runtime = Runtime::Current();
        if (runtime->UseJitCompilation()) {
            if (runtime->GetJit()->GetCodeCache()->ContainsPc(GetEntryPointFromQuickCompiledCode())) {
                SetEntryPointFromQuickCompiledCodePtrSize(
                        src->IsNative() ? GetQuickGenericJniStub() : GetQuickToInterpreterBridge(),
                        image_pointer_size);
            }
        }
        // Clear the profiling info for the same reasons as the JIT code.
        if (!src->IsNative()) {
            SetProfilingInfoPtrSize(nullptr, image_pointer_size);
        }
        // Clear hotness to let the JIT properly decide when to compile this method.
        hotness_count_ = 0;
    }

    bool ArtMethod::IsImagePointerSize(PointerSize pointer_size) {
        // Hijack this function to get access to PtrSizedFieldsOffset.
        //
        // Ensure that PrtSizedFieldsOffset is correct. We rely here on usually having both 32-bit and
        // 64-bit builds.
        static_assert(std::is_standard_layout<ArtMethod>::value, "ArtMethod is not standard layout.");
        static_assert(
                (sizeof(void*) != 4) ||
                (offsetof(ArtMethod, ptr_sized_fields_) == PtrSizedFieldsOffset(PointerSize::k32)),
                "Unexpected 32-bit class layout.");
        static_assert(
                (sizeof(void*) != 8) ||
                (offsetof(ArtMethod, ptr_sized_fields_) == PtrSizedFieldsOffset(PointerSize::k64)),
                "Unexpected 64-bit class layout.");

        Runtime* runtime = Runtime::Current();
        if (runtime == nullptr) {
            return true;
        }
        return runtime->GetClassLinker()->GetImagePointerSize() == pointer_size;
    }

    std::string ArtMethod::PrettyMethod(ArtMethod* m, bool with_signature) {
        if (m == nullptr) {
            return "null";
        }
        return m->PrettyMethod(with_signature);
    }

    std::string ArtMethod::PrettyMethod(bool with_signature) {
        if (UNLIKELY(IsRuntimeMethod())) {
            std::string result = GetDeclaringClassDescriptor();
            result += '.';
            result += GetName();
            // Do not add "<no signature>" even if `with_signature` is true.
            return result;
        }
        ArtMethod* m =
                GetInterfaceMethodIfProxy(Runtime::Current()->GetClassLinker()->GetImagePointerSize());
        std::string res(m->GetDexFile()->PrettyMethod(m->GetDexMethodIndex(), with_signature));
        if (with_signature && m->IsObsolete()) {
            return "<OBSOLETE> " + res;
        } else {
            return res;
        }
    }

    std::string ArtMethod::JniShortName() {
        return GetJniShortName(GetDeclaringClassDescriptor(), GetName());
    }

    std::string ArtMethod::JniLongName() {
        std::string long_name;
        long_name += JniShortName();
        long_name += "__";

        std::string signature(GetSignature().ToString());
        signature.erase(0, 1);
        signature.erase(signature.begin() + signature.find(')'), signature.end());

        long_name += MangleForJni(signature);

        return long_name;
    }

    const char* ArtMethod::GetRuntimeMethodName() {
        Runtime* const runtime = Runtime::Current();
        if (this == runtime->GetResolutionMethod()) {
            return "<runtime internal resolution method>";
        } else if (this == runtime->GetImtConflictMethod()) {
            return "<runtime internal imt conflict method>";
        } else if (this == runtime->GetCalleeSaveMethod(CalleeSaveType::kSaveAllCalleeSaves)) {
            return "<runtime internal callee-save all registers method>";
        } else if (this == runtime->GetCalleeSaveMethod(CalleeSaveType::kSaveRefsOnly)) {
            return "<runtime internal callee-save reference registers method>";
        } else if (this == runtime->GetCalleeSaveMethod(CalleeSaveType::kSaveRefsAndArgs)) {
            return "<runtime internal callee-save reference and argument registers method>";
        } else if (this == runtime->GetCalleeSaveMethod(CalleeSaveType::kSaveEverything)) {
            return "<runtime internal save-every-register method>";
        } else if (this == runtime->GetCalleeSaveMethod(CalleeSaveType::kSaveEverythingForClinit)) {
            return "<runtime internal save-every-register method for clinit>";
        } else if (this == runtime->GetCalleeSaveMethod(CalleeSaveType::kSaveEverythingForSuspendCheck)) {
            return "<runtime internal save-every-register method for suspend check>";
        } else {
            return "<unknown runtime internal method>";
        }
    }

// AssertSharedHeld doesn't work in GetAccessFlags, so use a NO_THREAD_SAFETY_ANALYSIS helper.
// TODO: Figure out why ASSERT_SHARED_CAPABILITY doesn't work.
    template <ReadBarrierOption kReadBarrierOption>
    ALWAYS_INLINE static inline void DoGetAccessFlagsHelper(ArtMethod* method)
    NO_THREAD_SAFETY_ANALYSIS {
    CHECK(method->IsRuntimeMethod() ||
    method->GetDeclaringClass<kReadBarrierOption>()->IsIdxLoaded() ||
    method->GetDeclaringClass<kReadBarrierOption>()->IsErroneous());
}

}  // namespace art
