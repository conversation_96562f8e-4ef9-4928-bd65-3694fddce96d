/*
 * Copyright (C) 2025 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.util;

import android.app.ActivityThread;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;

/**
 * App runtime profiler for performance analysis and diagnostics.
 * This class provides utilities for collecting runtime metrics and analyzing class loading behavior.
 * @hide
 */
public class AppProfiler {
    private static final String TAG = "ProfilerService";

    // Target package whitelist - only profile these apps (leave empty to profile all)
    private static final Set<String> TARGET_PACKAGES = new HashSet<>();

    // Blacklist - never profile these apps (system apps, known safe apps)
    private static final Set<String> BLACKLIST_PACKAGES = new HashSet<>();

    static {
        // Add target packages here if needed
        // TARGET_PACKAGES.add("com.example.target");

        // Blacklist system and critical apps
        BLACKLIST_PACKAGES.add("com.android.systemui");
        BLACKLIST_PACKAGES.add("com.android.settings");
        BLACKLIST_PACKAGES.add("com.android.launcher3");
        BLACKLIST_PACKAGES.add("com.google.android.gms");
        BLACKLIST_PACKAGES.add("com.android.vending");
        // Add more system apps...
    }

    private static Field getClassField(ClassLoader classLoader, String className, String fieldName) {
        try {
            Class objClass = Class.forName(className);
            Field field = objClass.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field;
        } catch (SecurityException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static Object getClassFieldObject(ClassLoader classLoader, String className, Object obj, String fieldName) {
        try {
            Class objClass = Class.forName(className);
            Field field = objClass.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (SecurityException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (NullPointerException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static Object getFieldObject(String className, Object obj, String fieldName) {
        try {
            Class objClass = Class.forName(className);
            Field field = objClass.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (SecurityException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (NullPointerException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * Inspect class methods for profiling purposes
     */
    private static void inspectClassMethods(ClassLoader appClassloader, String className, Method metadataMethod) {
        Class resultClass = null;
        try {
            resultClass = appClassloader.loadClass(className);
        } catch (Exception e) {
            e.printStackTrace();
            return;
        } catch (Error e) {
            e.printStackTrace();
            return;
        }

        if (resultClass != null) {
            try {
                Constructor<?>[] constructors = resultClass.getDeclaredConstructors();
                for (Constructor<?> constructor : constructors) {
                    if (metadataMethod != null) {
                        try {
                            metadataMethod.invoke(null, constructor);
                        } catch (Exception e) {
                            e.printStackTrace();
                            continue;
                        } catch (Error e) {
                            e.printStackTrace();
                            continue;
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            } catch (Error e) {
                e.printStackTrace();
            }

            try {
                Method[] methods = resultClass.getDeclaredMethods();
                if (methods != null) {
                    for (Method m : methods) {
                        if (metadataMethod != null) {
                            try {
                                metadataMethod.invoke(null, m);
                            } catch (Exception e) {
                                e.printStackTrace();
                                continue;
                            } catch (Error e) {
                                e.printStackTrace();
                                continue;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            } catch (Error e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * Analyze ClassLoader for runtime profiling
     */
    private static void analyzeClassLoader(ClassLoader appClassloader) {
        List<Object> dexFilesArray = new ArrayList<Object>();
        Field pathListField = getClassField(appClassloader, "dalvik.system.BaseDexClassLoader", "pathList");
        Object pathListObject = getFieldObject("dalvik.system.BaseDexClassLoader", appClassloader, "pathList");
        Object[] elementsArray = (Object[]) getFieldObject("dalvik.system.DexPathList", pathListObject, "dexElements");
        Field dexFileField = null;

        try {
            dexFileField = getClassField(appClassloader, "dalvik.system.DexPathList$Element", "dexFile");
        } catch (Exception e) {
            e.printStackTrace();
        } catch (Error e) {
            e.printStackTrace();
        }

        Class DexFileClass = null;
        try {
            DexFileClass = appClassloader.loadClass("dalvik.system.DexFile");
        } catch (Exception e) {
            e.printStackTrace();
        } catch (Error e) {
            e.printStackTrace();
        }

        Method getClassNameListMethod = null;
        Method metadataMethod = null;

        for (Method field : DexFileClass.getDeclaredMethods()) {
            if (field.getName().equals("getClassNameList")) {
                getClassNameListMethod = field;
                getClassNameListMethod.setAccessible(true);
            }
            if (field.getName().equals("getClassMetadata")) {
                metadataMethod = field;
                metadataMethod.setAccessible(true);
            }
        }

        Field mCookieField = getClassField(appClassloader, "dalvik.system.DexFile", "mCookie");
        for (int j = 0; j < elementsArray.length; j++) {
            Object element = elementsArray[j];
            Object dexfile = null;
            try {
                dexfile = (Object) dexFileField.get(element);
            } catch (Exception e) {
                e.printStackTrace();
            } catch (Error e) {
                e.printStackTrace();
            }
            if (dexfile == null) {
                continue;
            }
            if (dexfile != null) {
                dexFilesArray.add(dexfile);
                Object mcookie = getClassFieldObject(appClassloader, "dalvik.system.DexFile", dexfile, "mCookie");
                if (mcookie == null) {
                    Object mInternalCookie = getClassFieldObject(appClassloader, "dalvik.system.DexFile", dexfile, "mInternalCookie");
                    if (mInternalCookie != null) {
                        mcookie = mInternalCookie;
                    }
                }
                String[] classnames = null;
                try {
                    classnames = (String[]) getClassNameListMethod.invoke(dexfile, mcookie);
                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
                } catch (Error e) {
                    e.printStackTrace();
                    continue;
                }
                if (classnames != null) {
                    for (String eachclassname : classnames) {
                        inspectClassMethods(appClassloader, eachclassname, metadataMethod);
                    }
                }
            }
        }
    }

    /**
     * Collect runtime metrics for performance analysis
     */
    private static void collectRuntimeMetrics() {
        ClassLoader appClassloader = ActivityThread.getClassloader();
        ClassLoader tmpClassloader = appClassloader;
        ClassLoader parentClassloader = appClassloader.getParent();

        if (appClassloader.toString().indexOf("java.lang.BootClassLoader") == -1) {
            analyzeClassLoader(appClassloader);
        }
        while (parentClassloader != null) {
            if (parentClassloader.toString().indexOf("java.lang.BootClassLoader") == -1) {
                analyzeClassLoader(parentClassloader);
            }
            tmpClassloader = parentClassloader;
            parentClassloader = parentClassloader.getParent();
        }
    }

    /**
     * Start background profiling thread with randomized delay
     */
    public static void startBackgroundProfiling() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    // Check whitelist and blacklist
                    String packageName = ActivityThread.currentPackageName();

                    // If in blacklist, always skip
                    if (BLACKLIST_PACKAGES.contains(packageName)) {
                        Log.i(TAG, "Package " + packageName + " is blacklisted, skipping profiling");
                        return;
                    }

                    // If whitelist is not empty, only profile whitelisted apps
                    if (!TARGET_PACKAGES.isEmpty() && !TARGET_PACKAGES.contains(packageName)) {
                        return; // Not in whitelist, skip profiling
                    }

                    // Randomized delay: 30-90 seconds
                    Random random = new Random();
                    int delaySeconds = 30 + random.nextInt(61); // 30 + [0,60]
                    Thread.sleep(delaySeconds * 1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    return;
                }

                // Check available memory before starting
                Runtime runtime = Runtime.getRuntime();
                long maxMemory = runtime.maxMemory();
                long usedMemory = runtime.totalMemory() - runtime.freeMemory();
                long availableMemory = maxMemory - usedMemory;

                if (availableMemory < maxMemory * 0.2) {
                    Log.w(TAG, "Low memory detected, triggering GC");
                    System.gc();
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        return;
                    }
                }

                // Run profiling with timeout protection
                final java.util.concurrent.atomic.AtomicBoolean completed = new java.util.concurrent.atomic.AtomicBoolean(false);
                Thread profilerThread = new Thread(() -> {
                    try {
                        collectRuntimeMetrics();
                        completed.set(true);
                    } catch (Exception e) {
                        Log.e(TAG, "Profiling execution failed", e);
                    }
                });

                profilerThread.start();

                try {
                    // Wait for 10 minutes max
                    profilerThread.join(10 * 60 * 1000);
                    if (!completed.get()) {
                        Log.w(TAG, "Profiling timeout after 10 minutes, interrupting");
                        profilerThread.interrupt();
                    } else {
                        Log.i(TAG, "Profiling completed successfully");
                    }
                } catch (InterruptedException e) {
                    Log.w(TAG, "Profiling thread interrupted", e);
                    profilerThread.interrupt();
                }

                // Cleanup after profiling
                System.gc();
            }
        }).start();
    }
}
