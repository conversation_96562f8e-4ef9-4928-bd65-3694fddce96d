# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
总是使用中文回答
### FART 脱壳精髓

**主动调用 + 双重拦截 + 直接修复**

```
传统被动脱壳：等待 APP 自己调用方法 → 覆盖率低
FART 主动调用：强制触发所有方法解密 → 覆盖率高
直接修复：原地写入解密后的 CodeItem → 无需 Python 后处理
```

### 1. 编译系统

```bash
# 1. 初始化环境
cd <AOSP_ROOT>
source build/envsetup.sh
lunch <device>-userdebug

# 2. 增量编译（推荐）
mmm art/runtime
mmm frameworks/base
mmm libcore

# 3. 或完整编译
make -j$(nproc)

# 4. 生成 OTA 包
make otapackage
```

### 2. 刷入设备

```bash
# 重启到 Recovery
adb reboot recovery

# 刷入 OTA 包
adb sideload out/target/product/<device>/lineage-*.zip
```

### 3. 使用脱壳

```bash
# 1. 安装目标应用
adb install target_app.apk

# 2. 授予存储权限
adb shell pm grant <package_name> android.permission.WRITE_EXTERNAL_STORAGE

# 3. 启动应用（自动脱壳，等待 30-90 秒随机延迟）

# 4. 查看输出
adb shell ls /data/data/<package_name>/cyrus/

# 5. 拉取文件
adb pull /data/data/<package_name>/cyrus/ ./fart_output/

# 6. 直接反编译（无需修复）
jadx fart_output/*_dex_file_repaired.dex -d output/
```
### 关键创新点

| 特性 | 说明 | 优势 |
|------|------|------|
| **self=nullptr 标记** | 主动调用时传入 null Thread 指针 | 区分主动调用和正常调用 |
| **双重拦截点** | Invoke + Execute | 应对不同加壳策略 |
| **直接修复** | lseek + write 原地写入 | 性能提升 2.6 倍，无需 Python |
| **系统工具过滤** | isValidAndroidApp() | 避免 dex2oat 干扰 |

## Code Style Notes

- C++ code follows AOSP style (4-space indent)
- Use std::string for path manipulation to avoid buffer overflows
- Always check syscall return values (read/write/open)
- Log errors with LOG(ERROR) for debugging

## Important Security Context

This is a defensive security research tool for:
- Understanding Android packing mechanisms
- Analyzing malware protection techniques
- Educational purposes in reverse engineering

Do NOT use for:
- Bypassing app protections for piracy
- Extracting credentials or sensitive data
- Commercial cracking activities

## References

- Original FART: https://github.com/hanbinglengyue/FART
- Author blog: https://cyrus-studio.github.io/blog/
- Android DEX format: https://source.android.com/docs/core/runtime/dex-format
- Aandrid10 ART source: https://cs.android.com/android/platform/superproject/+/android-10.0.0_r1:art/
- Android14 ART source: https://cs.android.com/android/platform/superproject/+/android-14.0.0_r45:art/

