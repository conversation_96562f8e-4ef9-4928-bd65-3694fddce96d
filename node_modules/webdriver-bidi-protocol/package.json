{"name": "webdriver-bidi-protocol", "version": "0.3.6", "description": "", "main": "out/index.js", "types": "out/index.d.ts", "keywords": ["webdriver", "bidi", "protocol"], "author": "The Chromium Authors", "license": "Apache-2.0", "scripts": {"test": "./tools/build.sh", "format": "prettier -w 'out/**/*' 'src/**/*' 'test-d/**/*'"}, "repository": {"type": "git", "url": "git+https://github.com/GoogleChromeLabs/webdriver-bidi-protocol.git"}, "devDependencies": {"@types/node": "^24.0.4", "parse5": "^8.0.0", "prettier": "3.6.2", "tsd": "0.33.0", "typescript": "5.9.2", "ts-morph": "27.0.0"}}