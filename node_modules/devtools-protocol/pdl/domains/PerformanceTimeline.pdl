# Copyright 2017 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.
#
# Contributing to Chrome DevTools Protocol: https://goo.gle/devtools-contribution-guide-cdp

# Reporting of performance timeline events, as specified in
# https://w3c.github.io/performance-timeline/#dom-performanceobserver.
experimental domain PerformanceTimeline
  depends on DOM
  depends on Network

  # See https://github.com/WICG/LargestContentfulPaint and largest_contentful_paint.idl
  type LargestContentfulPaint extends object
    properties
      Network.TimeSinceEpoch renderTime
      Network.TimeSinceEpoch loadTime
      # The number of pixels being painted.
      number size
      # The id attribute of the element, if available.
      optional string elementId
      # The URL of the image (may be trimmed).
      optional string url
      optional DOM.BackendNodeId nodeId

  type LayoutShiftAttribution extends object
    properties
      DOM.Rect previousRect
      DOM.Rect currentRect
      optional DOM.BackendNodeId nodeId

  # See https://wicg.github.io/layout-instability/#sec-layout-shift and layout_shift.idl
  type LayoutShift extends object
    properties
      # Score increment produced by this event.
      number value
      boolean hadRecentInput
      Network.TimeSinceEpoch lastInputTime
      array of LayoutShiftAttribution sources

  type TimelineEvent extends object
    properties
      # Identifies the frame that this event is related to. Empty for non-frame targets.
      Page.FrameId frameId
      # The event type, as specified in https://w3c.github.io/performance-timeline/#dom-performanceentry-entrytype
      # This determines which of the optional "details" fields is present.
      string type
      # Name may be empty depending on the type.
      string name
      # Time in seconds since Epoch, monotonically increasing within document lifetime.
      Network.TimeSinceEpoch time
      # Event duration, if applicable.
      optional number duration
      optional LargestContentfulPaint lcpDetails
      optional LayoutShift layoutShiftDetails

  # Previously buffered events would be reported before method returns.
  # See also: timelineEventAdded
  command enable
    parameters
      # The types of event to report, as specified in
      # https://w3c.github.io/performance-timeline/#dom-performanceentry-entrytype
      # The specified filter overrides any previous filters, passing empty
      # filter disables recording.
      # Note that not all types exposed to the web platform are currently supported.
      array of string eventTypes

  # Sent when a performance timeline event is added. See reportPerformanceTimeline method.
  event timelineEventAdded
    parameters
      TimelineEvent event
