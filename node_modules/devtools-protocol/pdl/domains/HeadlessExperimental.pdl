# Copyright 2017 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.
#
# Contributing to Chrome DevTools Protocol: https://goo.gle/devtools-contribution-guide-cdp

# This domain provides experimental commands only supported in headless mode.
experimental domain HeadlessExperimental
  depends on Page
  depends on Runtime

  # Encoding options for a screenshot.
  type ScreenshotParams extends object
    properties
      # Image compression format (defaults to png).
      optional enum format
        jpeg
        png
        webp
      # Compression quality from range [0..100] (jpeg and webp only).
      optional integer quality
      # Optimize image encoding for speed, not for resulting size (defaults to false)
      optional boolean optimizeForSpeed

  # Sends a BeginFrame to the target and returns when the frame was completed. Optionally captures a
  # screenshot from the resulting frame. Requires that the target was created with enabled
  # BeginFrameControl. Designed for use with --run-all-compositor-stages-before-draw, see also
  # https://goo.gle/chrome-headless-rendering for more background.
  command beginFrame
    parameters
      # Timestamp of this BeginFrame in Renderer TimeTicks (milliseconds of uptime). If not set,
      # the current time will be used.
      optional number frameTimeTicks
      # The interval between BeginFrames that is reported to the compositor, in milliseconds.
      # Defaults to a 60 frames/second interval, i.e. about 16.666 milliseconds.
      optional number interval
      # Whether updates should not be committed and drawn onto the display. False by default. If
      # true, only side effects of the BeginFrame will be run, such as layout and animations, but
      # any visual updates may not be visible on the display or in screenshots.
      optional boolean noDisplayUpdates
      # If set, a screenshot of the frame will be captured and returned in the response. Otherwise,
      # no screenshot will be captured. Note that capturing a screenshot can fail, for example,
      # during renderer initialization. In such a case, no screenshot data will be returned.
      optional ScreenshotParams screenshot
    returns
      # Whether the BeginFrame resulted in damage and, thus, a new frame was committed to the
      # display. Reported for diagnostic uses, may be removed in the future.
      boolean hasDamage
      # Base64-encoded image data of the screenshot, if one was requested and successfully taken.
      optional binary screenshotData

  # Disables headless events for the target.
  deprecated command disable

  # Enables headless events for the target.
  deprecated command enable
