/**
 * Copyright 2024 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * THIS FILE IS AUTOGENERATED by cddlconv 0.1.7.
 * Run `node tools/generate-bidi-types.mjs` to regenerate.
 * @see https://github.com/w3c/webdriver-bidi/blob/master/index.bs
 */
export declare namespace Speculation {
    const enum PreloadingStatus {
        Pending = "pending",
        Ready = "ready",
        Success = "success",
        Failure = "failure"
    }
}
export type SpeculationEvent = Speculation.PrefetchStatusUpdated;
export declare namespace Speculation {
    type PrefetchStatusUpdated = {
        method: 'speculation.prefetchStatusUpdated';
        params: Speculation.PrefetchStatusUpdatedParameters;
    };
}
export declare namespace Speculation {
    type PrefetchStatusUpdatedParameters = {
        context: string;
        url: string;
        status: Speculation.PreloadingStatus;
    };
}
