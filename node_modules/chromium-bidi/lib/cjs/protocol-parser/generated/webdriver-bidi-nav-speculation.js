"use strict";
/**
 * Copyright 2024 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpeculationEventSchema = exports.Speculation = void 0;
/**
 * THIS FILE IS AUTOGENERATED by cddlconv 0.1.7.
 * Run `node tools/generate-bidi-types.mjs` to regenerate.
 * @see https://github.com/w3c/webdriver-bidi/blob/master/index.bs
 */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck Some types may be circular.
const zod_1 = __importDefault(require("zod"));
var Speculation;
(function (Speculation) {
    Speculation.PreloadingStatusSchema = zod_1.default.lazy(() => zod_1.default.enum(['pending', 'ready', 'success', 'failure']));
})(Speculation || (exports.Speculation = Speculation = {}));
exports.SpeculationEventSchema = zod_1.default.lazy(() => Speculation.PrefetchStatusUpdatedSchema);
(function (Speculation) {
    Speculation.PrefetchStatusUpdatedSchema = zod_1.default.lazy(() => zod_1.default.object({
        method: zod_1.default.literal('speculation.prefetchStatusUpdated'),
        params: Speculation.PrefetchStatusUpdatedParametersSchema,
    }));
})(Speculation || (exports.Speculation = Speculation = {}));
(function (Speculation) {
    Speculation.PrefetchStatusUpdatedParametersSchema = zod_1.default.lazy(() => zod_1.default.object({
        context: zod_1.default.string(),
        url: zod_1.default.string(),
        status: Speculation.PreloadingStatusSchema,
    }));
})(Speculation || (exports.Speculation = Speculation = {}));
//# sourceMappingURL=webdriver-bidi-nav-speculation.js.map