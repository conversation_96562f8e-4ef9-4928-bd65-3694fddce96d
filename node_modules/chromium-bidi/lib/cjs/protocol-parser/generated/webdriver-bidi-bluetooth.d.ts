/**
 * Copyright 2024 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * THIS FILE IS AUTOGENERATED by cddlconv 0.1.7.
 * Run `node tools/generate-bidi-types.mjs` to regenerate.
 * @see https://github.com/w3c/webdriver-bidi/blob/master/index.bs
 */
import z from 'zod';
export declare namespace Bluetooth {
    const BluetoothUuidSchema: z.ZodLazy<z.ZodString>;
}
export declare namespace Bluetooth {
    const BluetoothManufacturerDataSchema: z.ZodLazy<z.ZodObject<{
        key: z.ZodN<PERSON>ber;
        data: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        key: number;
        data: string;
    }, {
        key: number;
        data: string;
    }>>;
}
export declare namespace Bluetooth {
    const CharacteristicPropertiesSchema: z.ZodLazy<z.ZodObject<{
        broadcast: z.ZodOptional<z.ZodBoolean>;
        read: z.ZodOptional<z.ZodBoolean>;
        writeWithoutResponse: z.ZodOptional<z.ZodBoolean>;
        write: z.ZodOptional<z.ZodBoolean>;
        notify: z.ZodOptional<z.ZodBoolean>;
        indicate: z.ZodOptional<z.ZodBoolean>;
        authenticatedSignedWrites: z.ZodOptional<z.ZodBoolean>;
        extendedProperties: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        read?: boolean | undefined;
        write?: boolean | undefined;
        broadcast?: boolean | undefined;
        writeWithoutResponse?: boolean | undefined;
        notify?: boolean | undefined;
        indicate?: boolean | undefined;
        authenticatedSignedWrites?: boolean | undefined;
        extendedProperties?: boolean | undefined;
    }, {
        read?: boolean | undefined;
        write?: boolean | undefined;
        broadcast?: boolean | undefined;
        writeWithoutResponse?: boolean | undefined;
        notify?: boolean | undefined;
        indicate?: boolean | undefined;
        authenticatedSignedWrites?: boolean | undefined;
        extendedProperties?: boolean | undefined;
    }>>;
}
export declare namespace Bluetooth {
    const RequestDeviceSchema: z.ZodLazy<z.ZodString>;
}
export declare namespace Bluetooth {
    const RequestDeviceInfoSchema: z.ZodLazy<z.ZodObject<{
        id: z.ZodLazy<z.ZodString>;
        name: z.ZodUnion<[z.ZodString, z.ZodNull]>;
    }, "strip", z.ZodTypeAny, {
        name: string | null;
        id: string;
    }, {
        name: string | null;
        id: string;
    }>>;
}
export declare namespace Bluetooth {
    const RequestDevicePromptSchema: z.ZodLazy<z.ZodString>;
}
export declare namespace Bluetooth {
    const ScanRecordSchema: z.ZodLazy<z.ZodObject<{
        name: z.ZodOptional<z.ZodString>;
        uuids: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodString>, "many">>;
        appearance: z.ZodOptional<z.ZodNumber>;
        manufacturerData: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodObject<{
            key: z.ZodNumber;
            data: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            key: number;
            data: string;
        }, {
            key: number;
            data: string;
        }>>, "many">>;
    }, "strip", z.ZodTypeAny, {
        name?: string | undefined;
        uuids?: string[] | undefined;
        appearance?: number | undefined;
        manufacturerData?: {
            key: number;
            data: string;
        }[] | undefined;
    }, {
        name?: string | undefined;
        uuids?: string[] | undefined;
        appearance?: number | undefined;
        manufacturerData?: {
            key: number;
            data: string;
        }[] | undefined;
    }>>;
}
export declare const BluetoothCommandSchema: z.ZodLazy<z.ZodUnion<[z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"bluetooth.handleRequestDevicePrompt">;
    params: z.ZodLazy<z.ZodIntersection<z.ZodObject<{
        context: z.ZodString;
        prompt: z.ZodLazy<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        prompt: string;
        context: string;
    }, {
        prompt: string;
        context: string;
    }>, z.ZodUnion<[z.ZodLazy<z.ZodObject<{
        accept: z.ZodLiteral<true>;
        device: z.ZodLazy<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        accept: true;
        device: string;
    }, {
        accept: true;
        device: string;
    }>>, z.ZodLazy<z.ZodObject<{
        accept: z.ZodLiteral<false>;
    }, "strip", z.ZodTypeAny, {
        accept: false;
    }, {
        accept: false;
    }>>]>>>;
}, "strip", z.ZodTypeAny, {
    params: {
        prompt: string;
        context: string;
    } & ({
        accept: true;
        device: string;
    } | {
        accept: false;
    });
    method: "bluetooth.handleRequestDevicePrompt";
}, {
    params: {
        prompt: string;
        context: string;
    } & ({
        accept: true;
        device: string;
    } | {
        accept: false;
    });
    method: "bluetooth.handleRequestDevicePrompt";
}>>, z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"bluetooth.simulateAdapter">;
    params: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        leSupported: z.ZodOptional<z.ZodBoolean>;
        state: z.ZodEnum<["absent", "powered-off", "powered-on"]>;
    }, "strip", z.ZodTypeAny, {
        context: string;
        state: "absent" | "powered-off" | "powered-on";
        leSupported?: boolean | undefined;
    }, {
        context: string;
        state: "absent" | "powered-off" | "powered-on";
        leSupported?: boolean | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        context: string;
        state: "absent" | "powered-off" | "powered-on";
        leSupported?: boolean | undefined;
    };
    method: "bluetooth.simulateAdapter";
}, {
    params: {
        context: string;
        state: "absent" | "powered-off" | "powered-on";
        leSupported?: boolean | undefined;
    };
    method: "bluetooth.simulateAdapter";
}>>, z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"bluetooth.disableSimulation">;
    params: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        context: string;
    }, {
        context: string;
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        context: string;
    };
    method: "bluetooth.disableSimulation";
}, {
    params: {
        context: string;
    };
    method: "bluetooth.disableSimulation";
}>>, z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"bluetooth.simulatePreconnectedPeripheral">;
    params: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        name: z.ZodString;
        manufacturerData: z.ZodArray<z.ZodLazy<z.ZodObject<{
            key: z.ZodNumber;
            data: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            key: number;
            data: string;
        }, {
            key: number;
            data: string;
        }>>, "many">;
        knownServiceUuids: z.ZodArray<z.ZodLazy<z.ZodString>, "many">;
    }, "strip", z.ZodTypeAny, {
        context: string;
        name: string;
        manufacturerData: {
            key: number;
            data: string;
        }[];
        address: string;
        knownServiceUuids: string[];
    }, {
        context: string;
        name: string;
        manufacturerData: {
            key: number;
            data: string;
        }[];
        address: string;
        knownServiceUuids: string[];
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        context: string;
        name: string;
        manufacturerData: {
            key: number;
            data: string;
        }[];
        address: string;
        knownServiceUuids: string[];
    };
    method: "bluetooth.simulatePreconnectedPeripheral";
}, {
    params: {
        context: string;
        name: string;
        manufacturerData: {
            key: number;
            data: string;
        }[];
        address: string;
        knownServiceUuids: string[];
    };
    method: "bluetooth.simulatePreconnectedPeripheral";
}>>, z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"bluetooth.simulateAdvertisement">;
    params: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        scanEntry: z.ZodLazy<z.ZodObject<{
            deviceAddress: z.ZodString;
            rssi: z.ZodNumber;
            scanRecord: z.ZodLazy<z.ZodObject<{
                name: z.ZodOptional<z.ZodString>;
                uuids: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodString>, "many">>;
                appearance: z.ZodOptional<z.ZodNumber>;
                manufacturerData: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodObject<{
                    key: z.ZodNumber;
                    data: z.ZodString;
                }, "strip", z.ZodTypeAny, {
                    key: number;
                    data: string;
                }, {
                    key: number;
                    data: string;
                }>>, "many">>;
            }, "strip", z.ZodTypeAny, {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            }, {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            }>>;
        }, "strip", z.ZodTypeAny, {
            deviceAddress: string;
            rssi: number;
            scanRecord: {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            };
        }, {
            deviceAddress: string;
            rssi: number;
            scanRecord: {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            };
        }>>;
    }, "strip", z.ZodTypeAny, {
        context: string;
        scanEntry: {
            deviceAddress: string;
            rssi: number;
            scanRecord: {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            };
        };
    }, {
        context: string;
        scanEntry: {
            deviceAddress: string;
            rssi: number;
            scanRecord: {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            };
        };
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        context: string;
        scanEntry: {
            deviceAddress: string;
            rssi: number;
            scanRecord: {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            };
        };
    };
    method: "bluetooth.simulateAdvertisement";
}, {
    params: {
        context: string;
        scanEntry: {
            deviceAddress: string;
            rssi: number;
            scanRecord: {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            };
        };
    };
    method: "bluetooth.simulateAdvertisement";
}>>, z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"bluetooth.simulateGattConnectionResponse">;
    params: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        code: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        code: number;
        context: string;
        address: string;
    }, {
        code: number;
        context: string;
        address: string;
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        code: number;
        context: string;
        address: string;
    };
    method: "bluetooth.simulateGattConnectionResponse";
}, {
    params: {
        code: number;
        context: string;
        address: string;
    };
    method: "bluetooth.simulateGattConnectionResponse";
}>>, z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"bluetooth.simulateGattDisconnection">;
    params: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        context: string;
        address: string;
    }, {
        context: string;
        address: string;
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        context: string;
        address: string;
    };
    method: "bluetooth.simulateGattDisconnection";
}, {
    params: {
        context: string;
        address: string;
    };
    method: "bluetooth.simulateGattDisconnection";
}>>, z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"bluetooth.simulateService">;
    params: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        uuid: z.ZodLazy<z.ZodString>;
        type: z.ZodEnum<["add", "remove"]>;
    }, "strip", z.ZodTypeAny, {
        uuid: string;
        type: "remove" | "add";
        context: string;
        address: string;
    }, {
        uuid: string;
        type: "remove" | "add";
        context: string;
        address: string;
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        uuid: string;
        type: "remove" | "add";
        context: string;
        address: string;
    };
    method: "bluetooth.simulateService";
}, {
    params: {
        uuid: string;
        type: "remove" | "add";
        context: string;
        address: string;
    };
    method: "bluetooth.simulateService";
}>>, z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"bluetooth.simulateCharacteristic">;
    params: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        serviceUuid: z.ZodLazy<z.ZodString>;
        characteristicUuid: z.ZodLazy<z.ZodString>;
        characteristicProperties: z.ZodOptional<z.ZodLazy<z.ZodObject<{
            broadcast: z.ZodOptional<z.ZodBoolean>;
            read: z.ZodOptional<z.ZodBoolean>;
            writeWithoutResponse: z.ZodOptional<z.ZodBoolean>;
            write: z.ZodOptional<z.ZodBoolean>;
            notify: z.ZodOptional<z.ZodBoolean>;
            indicate: z.ZodOptional<z.ZodBoolean>;
            authenticatedSignedWrites: z.ZodOptional<z.ZodBoolean>;
            extendedProperties: z.ZodOptional<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            read?: boolean | undefined;
            write?: boolean | undefined;
            broadcast?: boolean | undefined;
            writeWithoutResponse?: boolean | undefined;
            notify?: boolean | undefined;
            indicate?: boolean | undefined;
            authenticatedSignedWrites?: boolean | undefined;
            extendedProperties?: boolean | undefined;
        }, {
            read?: boolean | undefined;
            write?: boolean | undefined;
            broadcast?: boolean | undefined;
            writeWithoutResponse?: boolean | undefined;
            notify?: boolean | undefined;
            indicate?: boolean | undefined;
            authenticatedSignedWrites?: boolean | undefined;
            extendedProperties?: boolean | undefined;
        }>>>;
        type: z.ZodEnum<["add", "remove"]>;
    }, "strip", z.ZodTypeAny, {
        type: "remove" | "add";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        characteristicProperties?: {
            read?: boolean | undefined;
            write?: boolean | undefined;
            broadcast?: boolean | undefined;
            writeWithoutResponse?: boolean | undefined;
            notify?: boolean | undefined;
            indicate?: boolean | undefined;
            authenticatedSignedWrites?: boolean | undefined;
            extendedProperties?: boolean | undefined;
        } | undefined;
    }, {
        type: "remove" | "add";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        characteristicProperties?: {
            read?: boolean | undefined;
            write?: boolean | undefined;
            broadcast?: boolean | undefined;
            writeWithoutResponse?: boolean | undefined;
            notify?: boolean | undefined;
            indicate?: boolean | undefined;
            authenticatedSignedWrites?: boolean | undefined;
            extendedProperties?: boolean | undefined;
        } | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        type: "remove" | "add";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        characteristicProperties?: {
            read?: boolean | undefined;
            write?: boolean | undefined;
            broadcast?: boolean | undefined;
            writeWithoutResponse?: boolean | undefined;
            notify?: boolean | undefined;
            indicate?: boolean | undefined;
            authenticatedSignedWrites?: boolean | undefined;
            extendedProperties?: boolean | undefined;
        } | undefined;
    };
    method: "bluetooth.simulateCharacteristic";
}, {
    params: {
        type: "remove" | "add";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        characteristicProperties?: {
            read?: boolean | undefined;
            write?: boolean | undefined;
            broadcast?: boolean | undefined;
            writeWithoutResponse?: boolean | undefined;
            notify?: boolean | undefined;
            indicate?: boolean | undefined;
            authenticatedSignedWrites?: boolean | undefined;
            extendedProperties?: boolean | undefined;
        } | undefined;
    };
    method: "bluetooth.simulateCharacteristic";
}>>, z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"bluetooth.simulateCharacteristicResponse">;
    params: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        serviceUuid: z.ZodLazy<z.ZodString>;
        characteristicUuid: z.ZodLazy<z.ZodString>;
        type: z.ZodEnum<["read", "write", "subscribe-to-notifications", "unsubscribe-from-notifications"]>;
        code: z.ZodNumber;
        data: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
    }, "strip", z.ZodTypeAny, {
        code: number;
        type: "read" | "write" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        data?: number[] | undefined;
    }, {
        code: number;
        type: "read" | "write" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        data?: number[] | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        code: number;
        type: "read" | "write" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        data?: number[] | undefined;
    };
    method: "bluetooth.simulateCharacteristicResponse";
}, {
    params: {
        code: number;
        type: "read" | "write" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        data?: number[] | undefined;
    };
    method: "bluetooth.simulateCharacteristicResponse";
}>>, z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"bluetooth.simulateDescriptor">;
    params: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        serviceUuid: z.ZodLazy<z.ZodString>;
        characteristicUuid: z.ZodLazy<z.ZodString>;
        descriptorUuid: z.ZodLazy<z.ZodString>;
        type: z.ZodEnum<["add", "remove"]>;
    }, "strip", z.ZodTypeAny, {
        type: "remove" | "add";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        descriptorUuid: string;
    }, {
        type: "remove" | "add";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        descriptorUuid: string;
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        type: "remove" | "add";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        descriptorUuid: string;
    };
    method: "bluetooth.simulateDescriptor";
}, {
    params: {
        type: "remove" | "add";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        descriptorUuid: string;
    };
    method: "bluetooth.simulateDescriptor";
}>>, z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"bluetooth.simulateDescriptorResponse">;
    params: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        serviceUuid: z.ZodLazy<z.ZodString>;
        characteristicUuid: z.ZodLazy<z.ZodString>;
        descriptorUuid: z.ZodLazy<z.ZodString>;
        type: z.ZodEnum<["read", "write"]>;
        code: z.ZodNumber;
        data: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
    }, "strip", z.ZodTypeAny, {
        code: number;
        type: "read" | "write";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        descriptorUuid: string;
        data?: number[] | undefined;
    }, {
        code: number;
        type: "read" | "write";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        descriptorUuid: string;
        data?: number[] | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        code: number;
        type: "read" | "write";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        descriptorUuid: string;
        data?: number[] | undefined;
    };
    method: "bluetooth.simulateDescriptorResponse";
}, {
    params: {
        code: number;
        type: "read" | "write";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        descriptorUuid: string;
        data?: number[] | undefined;
    };
    method: "bluetooth.simulateDescriptorResponse";
}>>, z.ZodObject<{}, "strip", z.ZodTypeAny, {}, {}>]>>;
export declare namespace Bluetooth {
    const HandleRequestDevicePromptSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.handleRequestDevicePrompt">;
        params: z.ZodLazy<z.ZodIntersection<z.ZodObject<{
            context: z.ZodString;
            prompt: z.ZodLazy<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            prompt: string;
            context: string;
        }, {
            prompt: string;
            context: string;
        }>, z.ZodUnion<[z.ZodLazy<z.ZodObject<{
            accept: z.ZodLiteral<true>;
            device: z.ZodLazy<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            accept: true;
            device: string;
        }, {
            accept: true;
            device: string;
        }>>, z.ZodLazy<z.ZodObject<{
            accept: z.ZodLiteral<false>;
        }, "strip", z.ZodTypeAny, {
            accept: false;
        }, {
            accept: false;
        }>>]>>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            prompt: string;
            context: string;
        } & ({
            accept: true;
            device: string;
        } | {
            accept: false;
        });
        method: "bluetooth.handleRequestDevicePrompt";
    }, {
        params: {
            prompt: string;
            context: string;
        } & ({
            accept: true;
            device: string;
        } | {
            accept: false;
        });
        method: "bluetooth.handleRequestDevicePrompt";
    }>>;
}
export declare namespace Bluetooth {
    const HandleRequestDevicePromptParametersSchema: z.ZodLazy<z.ZodIntersection<z.ZodObject<{
        context: z.ZodString;
        prompt: z.ZodLazy<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        prompt: string;
        context: string;
    }, {
        prompt: string;
        context: string;
    }>, z.ZodUnion<[z.ZodLazy<z.ZodObject<{
        accept: z.ZodLiteral<true>;
        device: z.ZodLazy<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        accept: true;
        device: string;
    }, {
        accept: true;
        device: string;
    }>>, z.ZodLazy<z.ZodObject<{
        accept: z.ZodLiteral<false>;
    }, "strip", z.ZodTypeAny, {
        accept: false;
    }, {
        accept: false;
    }>>]>>>;
}
export declare namespace Bluetooth {
    const HandleRequestDevicePromptAcceptParametersSchema: z.ZodLazy<z.ZodObject<{
        accept: z.ZodLiteral<true>;
        device: z.ZodLazy<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        accept: true;
        device: string;
    }, {
        accept: true;
        device: string;
    }>>;
}
export declare namespace Bluetooth {
    const HandleRequestDevicePromptCancelParametersSchema: z.ZodLazy<z.ZodObject<{
        accept: z.ZodLiteral<false>;
    }, "strip", z.ZodTypeAny, {
        accept: false;
    }, {
        accept: false;
    }>>;
}
export declare namespace Bluetooth {
    const SimulateAdapterSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.simulateAdapter">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            leSupported: z.ZodOptional<z.ZodBoolean>;
            state: z.ZodEnum<["absent", "powered-off", "powered-on"]>;
        }, "strip", z.ZodTypeAny, {
            context: string;
            state: "absent" | "powered-off" | "powered-on";
            leSupported?: boolean | undefined;
        }, {
            context: string;
            state: "absent" | "powered-off" | "powered-on";
            leSupported?: boolean | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            context: string;
            state: "absent" | "powered-off" | "powered-on";
            leSupported?: boolean | undefined;
        };
        method: "bluetooth.simulateAdapter";
    }, {
        params: {
            context: string;
            state: "absent" | "powered-off" | "powered-on";
            leSupported?: boolean | undefined;
        };
        method: "bluetooth.simulateAdapter";
    }>>;
}
export declare namespace Bluetooth {
    const SimulateAdapterParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        leSupported: z.ZodOptional<z.ZodBoolean>;
        state: z.ZodEnum<["absent", "powered-off", "powered-on"]>;
    }, "strip", z.ZodTypeAny, {
        context: string;
        state: "absent" | "powered-off" | "powered-on";
        leSupported?: boolean | undefined;
    }, {
        context: string;
        state: "absent" | "powered-off" | "powered-on";
        leSupported?: boolean | undefined;
    }>>;
}
export declare namespace Bluetooth {
    const DisableSimulationSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.disableSimulation">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            context: string;
        }, {
            context: string;
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            context: string;
        };
        method: "bluetooth.disableSimulation";
    }, {
        params: {
            context: string;
        };
        method: "bluetooth.disableSimulation";
    }>>;
}
export declare namespace Bluetooth {
    const DisableSimulationParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        context: string;
    }, {
        context: string;
    }>>;
}
export declare namespace Bluetooth {
    const SimulatePreconnectedPeripheralSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.simulatePreconnectedPeripheral">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            address: z.ZodString;
            name: z.ZodString;
            manufacturerData: z.ZodArray<z.ZodLazy<z.ZodObject<{
                key: z.ZodNumber;
                data: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                key: number;
                data: string;
            }, {
                key: number;
                data: string;
            }>>, "many">;
            knownServiceUuids: z.ZodArray<z.ZodLazy<z.ZodString>, "many">;
        }, "strip", z.ZodTypeAny, {
            context: string;
            name: string;
            manufacturerData: {
                key: number;
                data: string;
            }[];
            address: string;
            knownServiceUuids: string[];
        }, {
            context: string;
            name: string;
            manufacturerData: {
                key: number;
                data: string;
            }[];
            address: string;
            knownServiceUuids: string[];
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            context: string;
            name: string;
            manufacturerData: {
                key: number;
                data: string;
            }[];
            address: string;
            knownServiceUuids: string[];
        };
        method: "bluetooth.simulatePreconnectedPeripheral";
    }, {
        params: {
            context: string;
            name: string;
            manufacturerData: {
                key: number;
                data: string;
            }[];
            address: string;
            knownServiceUuids: string[];
        };
        method: "bluetooth.simulatePreconnectedPeripheral";
    }>>;
}
export declare namespace Bluetooth {
    const SimulatePreconnectedPeripheralParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        name: z.ZodString;
        manufacturerData: z.ZodArray<z.ZodLazy<z.ZodObject<{
            key: z.ZodNumber;
            data: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            key: number;
            data: string;
        }, {
            key: number;
            data: string;
        }>>, "many">;
        knownServiceUuids: z.ZodArray<z.ZodLazy<z.ZodString>, "many">;
    }, "strip", z.ZodTypeAny, {
        context: string;
        name: string;
        manufacturerData: {
            key: number;
            data: string;
        }[];
        address: string;
        knownServiceUuids: string[];
    }, {
        context: string;
        name: string;
        manufacturerData: {
            key: number;
            data: string;
        }[];
        address: string;
        knownServiceUuids: string[];
    }>>;
}
export declare namespace Bluetooth {
    const SimulateAdvertisementSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.simulateAdvertisement">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            scanEntry: z.ZodLazy<z.ZodObject<{
                deviceAddress: z.ZodString;
                rssi: z.ZodNumber;
                scanRecord: z.ZodLazy<z.ZodObject<{
                    name: z.ZodOptional<z.ZodString>;
                    uuids: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodString>, "many">>;
                    appearance: z.ZodOptional<z.ZodNumber>;
                    manufacturerData: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodObject<{
                        key: z.ZodNumber;
                        data: z.ZodString;
                    }, "strip", z.ZodTypeAny, {
                        key: number;
                        data: string;
                    }, {
                        key: number;
                        data: string;
                    }>>, "many">>;
                }, "strip", z.ZodTypeAny, {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                }, {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                }>>;
            }, "strip", z.ZodTypeAny, {
                deviceAddress: string;
                rssi: number;
                scanRecord: {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                };
            }, {
                deviceAddress: string;
                rssi: number;
                scanRecord: {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                };
            }>>;
        }, "strip", z.ZodTypeAny, {
            context: string;
            scanEntry: {
                deviceAddress: string;
                rssi: number;
                scanRecord: {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                };
            };
        }, {
            context: string;
            scanEntry: {
                deviceAddress: string;
                rssi: number;
                scanRecord: {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                };
            };
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            context: string;
            scanEntry: {
                deviceAddress: string;
                rssi: number;
                scanRecord: {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                };
            };
        };
        method: "bluetooth.simulateAdvertisement";
    }, {
        params: {
            context: string;
            scanEntry: {
                deviceAddress: string;
                rssi: number;
                scanRecord: {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                };
            };
        };
        method: "bluetooth.simulateAdvertisement";
    }>>;
}
export declare namespace Bluetooth {
    const SimulateAdvertisementParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        scanEntry: z.ZodLazy<z.ZodObject<{
            deviceAddress: z.ZodString;
            rssi: z.ZodNumber;
            scanRecord: z.ZodLazy<z.ZodObject<{
                name: z.ZodOptional<z.ZodString>;
                uuids: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodString>, "many">>;
                appearance: z.ZodOptional<z.ZodNumber>;
                manufacturerData: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodObject<{
                    key: z.ZodNumber;
                    data: z.ZodString;
                }, "strip", z.ZodTypeAny, {
                    key: number;
                    data: string;
                }, {
                    key: number;
                    data: string;
                }>>, "many">>;
            }, "strip", z.ZodTypeAny, {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            }, {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            }>>;
        }, "strip", z.ZodTypeAny, {
            deviceAddress: string;
            rssi: number;
            scanRecord: {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            };
        }, {
            deviceAddress: string;
            rssi: number;
            scanRecord: {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            };
        }>>;
    }, "strip", z.ZodTypeAny, {
        context: string;
        scanEntry: {
            deviceAddress: string;
            rssi: number;
            scanRecord: {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            };
        };
    }, {
        context: string;
        scanEntry: {
            deviceAddress: string;
            rssi: number;
            scanRecord: {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            };
        };
    }>>;
}
export declare namespace Bluetooth {
    const SimulateAdvertisementScanEntryParametersSchema: z.ZodLazy<z.ZodObject<{
        deviceAddress: z.ZodString;
        rssi: z.ZodNumber;
        scanRecord: z.ZodLazy<z.ZodObject<{
            name: z.ZodOptional<z.ZodString>;
            uuids: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodString>, "many">>;
            appearance: z.ZodOptional<z.ZodNumber>;
            manufacturerData: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodObject<{
                key: z.ZodNumber;
                data: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                key: number;
                data: string;
            }, {
                key: number;
                data: string;
            }>>, "many">>;
        }, "strip", z.ZodTypeAny, {
            name?: string | undefined;
            uuids?: string[] | undefined;
            appearance?: number | undefined;
            manufacturerData?: {
                key: number;
                data: string;
            }[] | undefined;
        }, {
            name?: string | undefined;
            uuids?: string[] | undefined;
            appearance?: number | undefined;
            manufacturerData?: {
                key: number;
                data: string;
            }[] | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        deviceAddress: string;
        rssi: number;
        scanRecord: {
            name?: string | undefined;
            uuids?: string[] | undefined;
            appearance?: number | undefined;
            manufacturerData?: {
                key: number;
                data: string;
            }[] | undefined;
        };
    }, {
        deviceAddress: string;
        rssi: number;
        scanRecord: {
            name?: string | undefined;
            uuids?: string[] | undefined;
            appearance?: number | undefined;
            manufacturerData?: {
                key: number;
                data: string;
            }[] | undefined;
        };
    }>>;
}
export declare namespace Bluetooth {
    const SimulateGattConnectionResponseSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.simulateGattConnectionResponse">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            address: z.ZodString;
            code: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            code: number;
            context: string;
            address: string;
        }, {
            code: number;
            context: string;
            address: string;
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            code: number;
            context: string;
            address: string;
        };
        method: "bluetooth.simulateGattConnectionResponse";
    }, {
        params: {
            code: number;
            context: string;
            address: string;
        };
        method: "bluetooth.simulateGattConnectionResponse";
    }>>;
}
export declare namespace Bluetooth {
    const SimulateGattConnectionResponseParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        code: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        code: number;
        context: string;
        address: string;
    }, {
        code: number;
        context: string;
        address: string;
    }>>;
}
export declare namespace Bluetooth {
    const SimulateGattDisconnectionSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.simulateGattDisconnection">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            address: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            context: string;
            address: string;
        }, {
            context: string;
            address: string;
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            context: string;
            address: string;
        };
        method: "bluetooth.simulateGattDisconnection";
    }, {
        params: {
            context: string;
            address: string;
        };
        method: "bluetooth.simulateGattDisconnection";
    }>>;
}
export declare namespace Bluetooth {
    const SimulateGattDisconnectionParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        context: string;
        address: string;
    }, {
        context: string;
        address: string;
    }>>;
}
export declare namespace Bluetooth {
    const SimulateServiceSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.simulateService">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            address: z.ZodString;
            uuid: z.ZodLazy<z.ZodString>;
            type: z.ZodEnum<["add", "remove"]>;
        }, "strip", z.ZodTypeAny, {
            uuid: string;
            type: "remove" | "add";
            context: string;
            address: string;
        }, {
            uuid: string;
            type: "remove" | "add";
            context: string;
            address: string;
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            uuid: string;
            type: "remove" | "add";
            context: string;
            address: string;
        };
        method: "bluetooth.simulateService";
    }, {
        params: {
            uuid: string;
            type: "remove" | "add";
            context: string;
            address: string;
        };
        method: "bluetooth.simulateService";
    }>>;
}
export declare namespace Bluetooth {
    const SimulateServiceParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        uuid: z.ZodLazy<z.ZodString>;
        type: z.ZodEnum<["add", "remove"]>;
    }, "strip", z.ZodTypeAny, {
        uuid: string;
        type: "remove" | "add";
        context: string;
        address: string;
    }, {
        uuid: string;
        type: "remove" | "add";
        context: string;
        address: string;
    }>>;
}
export declare namespace Bluetooth {
    const SimulateCharacteristicSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.simulateCharacteristic">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            address: z.ZodString;
            serviceUuid: z.ZodLazy<z.ZodString>;
            characteristicUuid: z.ZodLazy<z.ZodString>;
            characteristicProperties: z.ZodOptional<z.ZodLazy<z.ZodObject<{
                broadcast: z.ZodOptional<z.ZodBoolean>;
                read: z.ZodOptional<z.ZodBoolean>;
                writeWithoutResponse: z.ZodOptional<z.ZodBoolean>;
                write: z.ZodOptional<z.ZodBoolean>;
                notify: z.ZodOptional<z.ZodBoolean>;
                indicate: z.ZodOptional<z.ZodBoolean>;
                authenticatedSignedWrites: z.ZodOptional<z.ZodBoolean>;
                extendedProperties: z.ZodOptional<z.ZodBoolean>;
            }, "strip", z.ZodTypeAny, {
                read?: boolean | undefined;
                write?: boolean | undefined;
                broadcast?: boolean | undefined;
                writeWithoutResponse?: boolean | undefined;
                notify?: boolean | undefined;
                indicate?: boolean | undefined;
                authenticatedSignedWrites?: boolean | undefined;
                extendedProperties?: boolean | undefined;
            }, {
                read?: boolean | undefined;
                write?: boolean | undefined;
                broadcast?: boolean | undefined;
                writeWithoutResponse?: boolean | undefined;
                notify?: boolean | undefined;
                indicate?: boolean | undefined;
                authenticatedSignedWrites?: boolean | undefined;
                extendedProperties?: boolean | undefined;
            }>>>;
            type: z.ZodEnum<["add", "remove"]>;
        }, "strip", z.ZodTypeAny, {
            type: "remove" | "add";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            characteristicProperties?: {
                read?: boolean | undefined;
                write?: boolean | undefined;
                broadcast?: boolean | undefined;
                writeWithoutResponse?: boolean | undefined;
                notify?: boolean | undefined;
                indicate?: boolean | undefined;
                authenticatedSignedWrites?: boolean | undefined;
                extendedProperties?: boolean | undefined;
            } | undefined;
        }, {
            type: "remove" | "add";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            characteristicProperties?: {
                read?: boolean | undefined;
                write?: boolean | undefined;
                broadcast?: boolean | undefined;
                writeWithoutResponse?: boolean | undefined;
                notify?: boolean | undefined;
                indicate?: boolean | undefined;
                authenticatedSignedWrites?: boolean | undefined;
                extendedProperties?: boolean | undefined;
            } | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            type: "remove" | "add";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            characteristicProperties?: {
                read?: boolean | undefined;
                write?: boolean | undefined;
                broadcast?: boolean | undefined;
                writeWithoutResponse?: boolean | undefined;
                notify?: boolean | undefined;
                indicate?: boolean | undefined;
                authenticatedSignedWrites?: boolean | undefined;
                extendedProperties?: boolean | undefined;
            } | undefined;
        };
        method: "bluetooth.simulateCharacteristic";
    }, {
        params: {
            type: "remove" | "add";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            characteristicProperties?: {
                read?: boolean | undefined;
                write?: boolean | undefined;
                broadcast?: boolean | undefined;
                writeWithoutResponse?: boolean | undefined;
                notify?: boolean | undefined;
                indicate?: boolean | undefined;
                authenticatedSignedWrites?: boolean | undefined;
                extendedProperties?: boolean | undefined;
            } | undefined;
        };
        method: "bluetooth.simulateCharacteristic";
    }>>;
}
export declare namespace Bluetooth {
    const SimulateCharacteristicParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        serviceUuid: z.ZodLazy<z.ZodString>;
        characteristicUuid: z.ZodLazy<z.ZodString>;
        characteristicProperties: z.ZodOptional<z.ZodLazy<z.ZodObject<{
            broadcast: z.ZodOptional<z.ZodBoolean>;
            read: z.ZodOptional<z.ZodBoolean>;
            writeWithoutResponse: z.ZodOptional<z.ZodBoolean>;
            write: z.ZodOptional<z.ZodBoolean>;
            notify: z.ZodOptional<z.ZodBoolean>;
            indicate: z.ZodOptional<z.ZodBoolean>;
            authenticatedSignedWrites: z.ZodOptional<z.ZodBoolean>;
            extendedProperties: z.ZodOptional<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            read?: boolean | undefined;
            write?: boolean | undefined;
            broadcast?: boolean | undefined;
            writeWithoutResponse?: boolean | undefined;
            notify?: boolean | undefined;
            indicate?: boolean | undefined;
            authenticatedSignedWrites?: boolean | undefined;
            extendedProperties?: boolean | undefined;
        }, {
            read?: boolean | undefined;
            write?: boolean | undefined;
            broadcast?: boolean | undefined;
            writeWithoutResponse?: boolean | undefined;
            notify?: boolean | undefined;
            indicate?: boolean | undefined;
            authenticatedSignedWrites?: boolean | undefined;
            extendedProperties?: boolean | undefined;
        }>>>;
        type: z.ZodEnum<["add", "remove"]>;
    }, "strip", z.ZodTypeAny, {
        type: "remove" | "add";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        characteristicProperties?: {
            read?: boolean | undefined;
            write?: boolean | undefined;
            broadcast?: boolean | undefined;
            writeWithoutResponse?: boolean | undefined;
            notify?: boolean | undefined;
            indicate?: boolean | undefined;
            authenticatedSignedWrites?: boolean | undefined;
            extendedProperties?: boolean | undefined;
        } | undefined;
    }, {
        type: "remove" | "add";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        characteristicProperties?: {
            read?: boolean | undefined;
            write?: boolean | undefined;
            broadcast?: boolean | undefined;
            writeWithoutResponse?: boolean | undefined;
            notify?: boolean | undefined;
            indicate?: boolean | undefined;
            authenticatedSignedWrites?: boolean | undefined;
            extendedProperties?: boolean | undefined;
        } | undefined;
    }>>;
}
export declare namespace Bluetooth {
    const SimulateCharacteristicResponseSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.simulateCharacteristicResponse">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            address: z.ZodString;
            serviceUuid: z.ZodLazy<z.ZodString>;
            characteristicUuid: z.ZodLazy<z.ZodString>;
            type: z.ZodEnum<["read", "write", "subscribe-to-notifications", "unsubscribe-from-notifications"]>;
            code: z.ZodNumber;
            data: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            code: number;
            type: "read" | "write" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            data?: number[] | undefined;
        }, {
            code: number;
            type: "read" | "write" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            data?: number[] | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            code: number;
            type: "read" | "write" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            data?: number[] | undefined;
        };
        method: "bluetooth.simulateCharacteristicResponse";
    }, {
        params: {
            code: number;
            type: "read" | "write" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            data?: number[] | undefined;
        };
        method: "bluetooth.simulateCharacteristicResponse";
    }>>;
}
export declare namespace Bluetooth {
    const SimulateCharacteristicResponseParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        serviceUuid: z.ZodLazy<z.ZodString>;
        characteristicUuid: z.ZodLazy<z.ZodString>;
        type: z.ZodEnum<["read", "write", "subscribe-to-notifications", "unsubscribe-from-notifications"]>;
        code: z.ZodNumber;
        data: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
    }, "strip", z.ZodTypeAny, {
        code: number;
        type: "read" | "write" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        data?: number[] | undefined;
    }, {
        code: number;
        type: "read" | "write" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        data?: number[] | undefined;
    }>>;
}
export declare namespace Bluetooth {
    const SimulateDescriptorSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.simulateDescriptor">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            address: z.ZodString;
            serviceUuid: z.ZodLazy<z.ZodString>;
            characteristicUuid: z.ZodLazy<z.ZodString>;
            descriptorUuid: z.ZodLazy<z.ZodString>;
            type: z.ZodEnum<["add", "remove"]>;
        }, "strip", z.ZodTypeAny, {
            type: "remove" | "add";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            descriptorUuid: string;
        }, {
            type: "remove" | "add";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            descriptorUuid: string;
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            type: "remove" | "add";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            descriptorUuid: string;
        };
        method: "bluetooth.simulateDescriptor";
    }, {
        params: {
            type: "remove" | "add";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            descriptorUuid: string;
        };
        method: "bluetooth.simulateDescriptor";
    }>>;
}
export declare namespace Bluetooth {
    const SimulateDescriptorParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        serviceUuid: z.ZodLazy<z.ZodString>;
        characteristicUuid: z.ZodLazy<z.ZodString>;
        descriptorUuid: z.ZodLazy<z.ZodString>;
        type: z.ZodEnum<["add", "remove"]>;
    }, "strip", z.ZodTypeAny, {
        type: "remove" | "add";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        descriptorUuid: string;
    }, {
        type: "remove" | "add";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        descriptorUuid: string;
    }>>;
}
export declare namespace Bluetooth {
    const SimulateDescriptorResponseSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.simulateDescriptorResponse">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            address: z.ZodString;
            serviceUuid: z.ZodLazy<z.ZodString>;
            characteristicUuid: z.ZodLazy<z.ZodString>;
            descriptorUuid: z.ZodLazy<z.ZodString>;
            type: z.ZodEnum<["read", "write"]>;
            code: z.ZodNumber;
            data: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            code: number;
            type: "read" | "write";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            descriptorUuid: string;
            data?: number[] | undefined;
        }, {
            code: number;
            type: "read" | "write";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            descriptorUuid: string;
            data?: number[] | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            code: number;
            type: "read" | "write";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            descriptorUuid: string;
            data?: number[] | undefined;
        };
        method: "bluetooth.simulateDescriptorResponse";
    }, {
        params: {
            code: number;
            type: "read" | "write";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            descriptorUuid: string;
            data?: number[] | undefined;
        };
        method: "bluetooth.simulateDescriptorResponse";
    }>>;
}
export declare namespace Bluetooth {
    const SimulateDescriptorResponseParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        serviceUuid: z.ZodLazy<z.ZodString>;
        characteristicUuid: z.ZodLazy<z.ZodString>;
        descriptorUuid: z.ZodLazy<z.ZodString>;
        type: z.ZodEnum<["read", "write"]>;
        code: z.ZodNumber;
        data: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
    }, "strip", z.ZodTypeAny, {
        code: number;
        type: "read" | "write";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        descriptorUuid: string;
        data?: number[] | undefined;
    }, {
        code: number;
        type: "read" | "write";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        descriptorUuid: string;
        data?: number[] | undefined;
    }>>;
}
export declare const BluetoothEventSchema: z.ZodLazy<z.ZodUnion<[z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"bluetooth.requestDevicePromptUpdated">;
    params: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        prompt: z.ZodLazy<z.ZodString>;
        devices: z.ZodArray<z.ZodLazy<z.ZodObject<{
            id: z.ZodLazy<z.ZodString>;
            name: z.ZodUnion<[z.ZodString, z.ZodNull]>;
        }, "strip", z.ZodTypeAny, {
            name: string | null;
            id: string;
        }, {
            name: string | null;
            id: string;
        }>>, "many">;
    }, "strip", z.ZodTypeAny, {
        prompt: string;
        context: string;
        devices: {
            name: string | null;
            id: string;
        }[];
    }, {
        prompt: string;
        context: string;
        devices: {
            name: string | null;
            id: string;
        }[];
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        prompt: string;
        context: string;
        devices: {
            name: string | null;
            id: string;
        }[];
    };
    method: "bluetooth.requestDevicePromptUpdated";
}, {
    params: {
        prompt: string;
        context: string;
        devices: {
            name: string | null;
            id: string;
        }[];
    };
    method: "bluetooth.requestDevicePromptUpdated";
}>>, z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"bluetooth.gattConnectionAttempted">;
    params: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        context: string;
        address: string;
    }, {
        context: string;
        address: string;
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        context: string;
        address: string;
    };
    method: "bluetooth.gattConnectionAttempted";
}, {
    params: {
        context: string;
        address: string;
    };
    method: "bluetooth.gattConnectionAttempted";
}>>]>>;
export declare namespace Bluetooth {
    const RequestDevicePromptUpdatedSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.requestDevicePromptUpdated">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            prompt: z.ZodLazy<z.ZodString>;
            devices: z.ZodArray<z.ZodLazy<z.ZodObject<{
                id: z.ZodLazy<z.ZodString>;
                name: z.ZodUnion<[z.ZodString, z.ZodNull]>;
            }, "strip", z.ZodTypeAny, {
                name: string | null;
                id: string;
            }, {
                name: string | null;
                id: string;
            }>>, "many">;
        }, "strip", z.ZodTypeAny, {
            prompt: string;
            context: string;
            devices: {
                name: string | null;
                id: string;
            }[];
        }, {
            prompt: string;
            context: string;
            devices: {
                name: string | null;
                id: string;
            }[];
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            prompt: string;
            context: string;
            devices: {
                name: string | null;
                id: string;
            }[];
        };
        method: "bluetooth.requestDevicePromptUpdated";
    }, {
        params: {
            prompt: string;
            context: string;
            devices: {
                name: string | null;
                id: string;
            }[];
        };
        method: "bluetooth.requestDevicePromptUpdated";
    }>>;
}
export declare namespace Bluetooth {
    const RequestDevicePromptUpdatedParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        prompt: z.ZodLazy<z.ZodString>;
        devices: z.ZodArray<z.ZodLazy<z.ZodObject<{
            id: z.ZodLazy<z.ZodString>;
            name: z.ZodUnion<[z.ZodString, z.ZodNull]>;
        }, "strip", z.ZodTypeAny, {
            name: string | null;
            id: string;
        }, {
            name: string | null;
            id: string;
        }>>, "many">;
    }, "strip", z.ZodTypeAny, {
        prompt: string;
        context: string;
        devices: {
            name: string | null;
            id: string;
        }[];
    }, {
        prompt: string;
        context: string;
        devices: {
            name: string | null;
            id: string;
        }[];
    }>>;
}
export declare namespace Bluetooth {
    const GattConnectionAttemptedSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.gattConnectionAttempted">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            address: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            context: string;
            address: string;
        }, {
            context: string;
            address: string;
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            context: string;
            address: string;
        };
        method: "bluetooth.gattConnectionAttempted";
    }, {
        params: {
            context: string;
            address: string;
        };
        method: "bluetooth.gattConnectionAttempted";
    }>>;
}
export declare namespace Bluetooth {
    const GattConnectionAttemptedParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        context: string;
        address: string;
    }, {
        context: string;
        address: string;
    }>>;
}
export declare namespace Bluetooth {
    const CharacteristicEventGeneratedSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.characteristicEventGenerated">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            address: z.ZodString;
            serviceUuid: z.ZodLazy<z.ZodString>;
            characteristicUuid: z.ZodLazy<z.ZodString>;
            type: z.ZodEnum<["read", "write-with-response", "write-without-response", "subscribe-to-notifications", "unsubscribe-from-notifications"]>;
            data: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            type: "write-with-response" | "write-without-response" | "read" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            data?: number[] | undefined;
        }, {
            type: "write-with-response" | "write-without-response" | "read" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            data?: number[] | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            type: "write-with-response" | "write-without-response" | "read" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            data?: number[] | undefined;
        };
        method: "bluetooth.characteristicEventGenerated";
    }, {
        params: {
            type: "write-with-response" | "write-without-response" | "read" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            data?: number[] | undefined;
        };
        method: "bluetooth.characteristicEventGenerated";
    }>>;
}
export declare namespace Bluetooth {
    const CharacteristicEventGeneratedParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        serviceUuid: z.ZodLazy<z.ZodString>;
        characteristicUuid: z.ZodLazy<z.ZodString>;
        type: z.ZodEnum<["read", "write-with-response", "write-without-response", "subscribe-to-notifications", "unsubscribe-from-notifications"]>;
        data: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
    }, "strip", z.ZodTypeAny, {
        type: "write-with-response" | "write-without-response" | "read" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        data?: number[] | undefined;
    }, {
        type: "write-with-response" | "write-without-response" | "read" | "subscribe-to-notifications" | "unsubscribe-from-notifications";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        data?: number[] | undefined;
    }>>;
}
export declare namespace Bluetooth {
    const DescriptorEventGeneratedSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.descriptorEventGenerated">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            address: z.ZodString;
            serviceUuid: z.ZodLazy<z.ZodString>;
            characteristicUuid: z.ZodLazy<z.ZodString>;
            descriptorUuid: z.ZodLazy<z.ZodString>;
            type: z.ZodEnum<["read", "write"]>;
            data: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            type: "read" | "write";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            descriptorUuid: string;
            data?: number[] | undefined;
        }, {
            type: "read" | "write";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            descriptorUuid: string;
            data?: number[] | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            type: "read" | "write";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            descriptorUuid: string;
            data?: number[] | undefined;
        };
        method: "bluetooth.descriptorEventGenerated";
    }, {
        params: {
            type: "read" | "write";
            context: string;
            address: string;
            serviceUuid: string;
            characteristicUuid: string;
            descriptorUuid: string;
            data?: number[] | undefined;
        };
        method: "bluetooth.descriptorEventGenerated";
    }>>;
}
export declare namespace Bluetooth {
    const DescriptorEventGeneratedParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        serviceUuid: z.ZodLazy<z.ZodString>;
        characteristicUuid: z.ZodLazy<z.ZodString>;
        descriptorUuid: z.ZodLazy<z.ZodString>;
        type: z.ZodEnum<["read", "write"]>;
        data: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
    }, "strip", z.ZodTypeAny, {
        type: "read" | "write";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        descriptorUuid: string;
        data?: number[] | undefined;
    }, {
        type: "read" | "write";
        context: string;
        address: string;
        serviceUuid: string;
        characteristicUuid: string;
        descriptorUuid: string;
        data?: number[] | undefined;
    }>>;
}
