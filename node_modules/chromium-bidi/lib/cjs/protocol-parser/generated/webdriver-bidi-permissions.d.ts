/**
 * Copyright 2024 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * THIS FILE IS AUTOGENERATED by cddlconv 0.1.7.
 * Run `node tools/generate-bidi-types.mjs` to regenerate.
 * @see https://github.com/w3c/webdriver-bidi/blob/master/index.bs
 */
import z from 'zod';
export declare const PermissionsCommandSchema: z.ZodLazy<z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"permissions.setPermission">;
    params: z.<PERSON><z.ZodObject<{
        descriptor: z.Z<PERSON><z.ZodObject<{
            name: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            name: string;
        }, {
            name: string;
        }>>;
        state: z.ZodLazy<z.ZodEnum<["granted", "denied", "prompt"]>>;
        origin: z.ZodString;
        userContext: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        origin: string;
        state: "granted" | "denied" | "prompt";
        descriptor: {
            name: string;
        };
        userContext?: string | undefined;
    }, {
        origin: string;
        state: "granted" | "denied" | "prompt";
        descriptor: {
            name: string;
        };
        userContext?: string | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        origin: string;
        state: "granted" | "denied" | "prompt";
        descriptor: {
            name: string;
        };
        userContext?: string | undefined;
    };
    method: "permissions.setPermission";
}, {
    params: {
        origin: string;
        state: "granted" | "denied" | "prompt";
        descriptor: {
            name: string;
        };
        userContext?: string | undefined;
    };
    method: "permissions.setPermission";
}>>>;
export declare namespace Permissions {
    const PermissionDescriptorSchema: z.ZodLazy<z.ZodObject<{
        name: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        name: string;
    }, {
        name: string;
    }>>;
}
export declare namespace Permissions {
    const PermissionStateSchema: z.ZodLazy<z.ZodEnum<["granted", "denied", "prompt"]>>;
}
export declare namespace Permissions {
    const SetPermissionSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"permissions.setPermission">;
        params: z.ZodLazy<z.ZodObject<{
            descriptor: z.ZodLazy<z.ZodObject<{
                name: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                name: string;
            }, {
                name: string;
            }>>;
            state: z.ZodLazy<z.ZodEnum<["granted", "denied", "prompt"]>>;
            origin: z.ZodString;
            userContext: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            origin: string;
            state: "granted" | "denied" | "prompt";
            descriptor: {
                name: string;
            };
            userContext?: string | undefined;
        }, {
            origin: string;
            state: "granted" | "denied" | "prompt";
            descriptor: {
                name: string;
            };
            userContext?: string | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            origin: string;
            state: "granted" | "denied" | "prompt";
            descriptor: {
                name: string;
            };
            userContext?: string | undefined;
        };
        method: "permissions.setPermission";
    }, {
        params: {
            origin: string;
            state: "granted" | "denied" | "prompt";
            descriptor: {
                name: string;
            };
            userContext?: string | undefined;
        };
        method: "permissions.setPermission";
    }>>;
}
export declare namespace Permissions {
    const SetPermissionParametersSchema: z.ZodLazy<z.ZodObject<{
        descriptor: z.ZodLazy<z.ZodObject<{
            name: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            name: string;
        }, {
            name: string;
        }>>;
        state: z.ZodLazy<z.ZodEnum<["granted", "denied", "prompt"]>>;
        origin: z.ZodString;
        userContext: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        origin: string;
        state: "granted" | "denied" | "prompt";
        descriptor: {
            name: string;
        };
        userContext?: string | undefined;
    }, {
        origin: string;
        state: "granted" | "denied" | "prompt";
        descriptor: {
            name: string;
        };
        userContext?: string | undefined;
    }>>;
}
