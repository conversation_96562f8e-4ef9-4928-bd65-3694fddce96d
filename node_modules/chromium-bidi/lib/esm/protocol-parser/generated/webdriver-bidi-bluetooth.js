/**
 * Copyright 2024 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * THIS FILE IS AUTOGENERATED by cddlconv 0.1.7.
 * Run `node tools/generate-bidi-types.mjs` to regenerate.
 * @see https://github.com/w3c/webdriver-bidi/blob/master/index.bs
 */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck Some types may be circular.
import z from 'zod';
export var Bluetooth;
(function (Bluetooth) {
    Bluetooth.BluetoothUuidSchema = z.lazy(() => z.string());
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.BluetoothManufacturerDataSchema = z.lazy(() => z.object({
        key: z.number().int().nonnegative(),
        data: z.string(),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.CharacteristicPropertiesSchema = z.lazy(() => z.object({
        broadcast: z.boolean().optional(),
        read: z.boolean().optional(),
        writeWithoutResponse: z.boolean().optional(),
        write: z.boolean().optional(),
        notify: z.boolean().optional(),
        indicate: z.boolean().optional(),
        authenticatedSignedWrites: z.boolean().optional(),
        extendedProperties: z.boolean().optional(),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.RequestDeviceSchema = z.lazy(() => z.string());
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.RequestDeviceInfoSchema = z.lazy(() => z.object({
        id: Bluetooth.RequestDeviceSchema,
        name: z.union([z.string(), z.null()]),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.RequestDevicePromptSchema = z.lazy(() => z.string());
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.ScanRecordSchema = z.lazy(() => z.object({
        name: z.string().optional(),
        uuids: z.array(Bluetooth.BluetoothUuidSchema).optional(),
        appearance: z.number().optional(),
        manufacturerData: z
            .array(Bluetooth.BluetoothManufacturerDataSchema)
            .optional(),
    }));
})(Bluetooth || (Bluetooth = {}));
export const BluetoothCommandSchema = z.lazy(() => z.union([
    Bluetooth.HandleRequestDevicePromptSchema,
    Bluetooth.SimulateAdapterSchema,
    Bluetooth.DisableSimulationSchema,
    Bluetooth.SimulatePreconnectedPeripheralSchema,
    Bluetooth.SimulateAdvertisementSchema,
    Bluetooth.SimulateGattConnectionResponseSchema,
    Bluetooth.SimulateGattDisconnectionSchema,
    Bluetooth.SimulateServiceSchema,
    Bluetooth.SimulateCharacteristicSchema,
    Bluetooth.SimulateCharacteristicResponseSchema,
    Bluetooth.SimulateDescriptorSchema,
    Bluetooth.SimulateDescriptorResponseSchema,
    z.object({}),
]));
(function (Bluetooth) {
    Bluetooth.HandleRequestDevicePromptSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.handleRequestDevicePrompt'),
        params: Bluetooth.HandleRequestDevicePromptParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.HandleRequestDevicePromptParametersSchema = z.lazy(() => z
        .object({
        context: z.string(),
        prompt: Bluetooth.RequestDevicePromptSchema,
    })
        .and(z.union([
        Bluetooth.HandleRequestDevicePromptAcceptParametersSchema,
        Bluetooth.HandleRequestDevicePromptCancelParametersSchema,
    ])));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.HandleRequestDevicePromptAcceptParametersSchema = z.lazy(() => z.object({
        accept: z.literal(true),
        device: Bluetooth.RequestDeviceSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.HandleRequestDevicePromptCancelParametersSchema = z.lazy(() => z.object({
        accept: z.literal(false),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateAdapterSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.simulateAdapter'),
        params: Bluetooth.SimulateAdapterParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateAdapterParametersSchema = z.lazy(() => z.object({
        context: z.string(),
        leSupported: z.boolean().optional(),
        state: z.enum(['absent', 'powered-off', 'powered-on']),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.DisableSimulationSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.disableSimulation'),
        params: Bluetooth.DisableSimulationParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.DisableSimulationParametersSchema = z.lazy(() => z.object({
        context: z.string(),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulatePreconnectedPeripheralSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.simulatePreconnectedPeripheral'),
        params: Bluetooth.SimulatePreconnectedPeripheralParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulatePreconnectedPeripheralParametersSchema = z.lazy(() => z.object({
        context: z.string(),
        address: z.string(),
        name: z.string(),
        manufacturerData: z.array(Bluetooth.BluetoothManufacturerDataSchema),
        knownServiceUuids: z.array(Bluetooth.BluetoothUuidSchema),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateAdvertisementSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.simulateAdvertisement'),
        params: Bluetooth.SimulateAdvertisementParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateAdvertisementParametersSchema = z.lazy(() => z.object({
        context: z.string(),
        scanEntry: Bluetooth.SimulateAdvertisementScanEntryParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateAdvertisementScanEntryParametersSchema = z.lazy(() => z.object({
        deviceAddress: z.string(),
        rssi: z.number(),
        scanRecord: Bluetooth.ScanRecordSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateGattConnectionResponseSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.simulateGattConnectionResponse'),
        params: Bluetooth.SimulateGattConnectionResponseParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateGattConnectionResponseParametersSchema = z.lazy(() => z.object({
        context: z.string(),
        address: z.string(),
        code: z.number().int().nonnegative(),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateGattDisconnectionSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.simulateGattDisconnection'),
        params: Bluetooth.SimulateGattDisconnectionParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateGattDisconnectionParametersSchema = z.lazy(() => z.object({
        context: z.string(),
        address: z.string(),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateServiceSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.simulateService'),
        params: Bluetooth.SimulateServiceParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateServiceParametersSchema = z.lazy(() => z.object({
        context: z.string(),
        address: z.string(),
        uuid: Bluetooth.BluetoothUuidSchema,
        type: z.enum(['add', 'remove']),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateCharacteristicSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.simulateCharacteristic'),
        params: Bluetooth.SimulateCharacteristicParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateCharacteristicParametersSchema = z.lazy(() => z.object({
        context: z.string(),
        address: z.string(),
        serviceUuid: Bluetooth.BluetoothUuidSchema,
        characteristicUuid: Bluetooth.BluetoothUuidSchema,
        characteristicProperties: Bluetooth.CharacteristicPropertiesSchema.optional(),
        type: z.enum(['add', 'remove']),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateCharacteristicResponseSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.simulateCharacteristicResponse'),
        params: Bluetooth.SimulateCharacteristicResponseParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateCharacteristicResponseParametersSchema = z.lazy(() => z.object({
        context: z.string(),
        address: z.string(),
        serviceUuid: Bluetooth.BluetoothUuidSchema,
        characteristicUuid: Bluetooth.BluetoothUuidSchema,
        type: z.enum([
            'read',
            'write',
            'subscribe-to-notifications',
            'unsubscribe-from-notifications',
        ]),
        code: z.number().int().nonnegative(),
        data: z.array(z.number().int().nonnegative()).optional(),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateDescriptorSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.simulateDescriptor'),
        params: Bluetooth.SimulateDescriptorParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateDescriptorParametersSchema = z.lazy(() => z.object({
        context: z.string(),
        address: z.string(),
        serviceUuid: Bluetooth.BluetoothUuidSchema,
        characteristicUuid: Bluetooth.BluetoothUuidSchema,
        descriptorUuid: Bluetooth.BluetoothUuidSchema,
        type: z.enum(['add', 'remove']),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateDescriptorResponseSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.simulateDescriptorResponse'),
        params: Bluetooth.SimulateDescriptorResponseParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.SimulateDescriptorResponseParametersSchema = z.lazy(() => z.object({
        context: z.string(),
        address: z.string(),
        serviceUuid: Bluetooth.BluetoothUuidSchema,
        characteristicUuid: Bluetooth.BluetoothUuidSchema,
        descriptorUuid: Bluetooth.BluetoothUuidSchema,
        type: z.enum(['read', 'write']),
        code: z.number().int().nonnegative(),
        data: z.array(z.number().int().nonnegative()).optional(),
    }));
})(Bluetooth || (Bluetooth = {}));
export const BluetoothEventSchema = z.lazy(() => z.union([
    Bluetooth.RequestDevicePromptUpdatedSchema,
    Bluetooth.GattConnectionAttemptedSchema,
]));
(function (Bluetooth) {
    Bluetooth.RequestDevicePromptUpdatedSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.requestDevicePromptUpdated'),
        params: Bluetooth.RequestDevicePromptUpdatedParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.RequestDevicePromptUpdatedParametersSchema = z.lazy(() => z.object({
        context: z.string(),
        prompt: Bluetooth.RequestDevicePromptSchema,
        devices: z.array(Bluetooth.RequestDeviceInfoSchema),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.GattConnectionAttemptedSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.gattConnectionAttempted'),
        params: Bluetooth.GattConnectionAttemptedParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.GattConnectionAttemptedParametersSchema = z.lazy(() => z.object({
        context: z.string(),
        address: z.string(),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.CharacteristicEventGeneratedSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.characteristicEventGenerated'),
        params: Bluetooth.CharacteristicEventGeneratedParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.CharacteristicEventGeneratedParametersSchema = z.lazy(() => z.object({
        context: z.string(),
        address: z.string(),
        serviceUuid: Bluetooth.BluetoothUuidSchema,
        characteristicUuid: Bluetooth.BluetoothUuidSchema,
        type: z.enum([
            'read',
            'write-with-response',
            'write-without-response',
            'subscribe-to-notifications',
            'unsubscribe-from-notifications',
        ]),
        data: z.array(z.number().int().nonnegative()).optional(),
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.DescriptorEventGeneratedSchema = z.lazy(() => z.object({
        method: z.literal('bluetooth.descriptorEventGenerated'),
        params: Bluetooth.DescriptorEventGeneratedParametersSchema,
    }));
})(Bluetooth || (Bluetooth = {}));
(function (Bluetooth) {
    Bluetooth.DescriptorEventGeneratedParametersSchema = z.lazy(() => z.object({
        context: z.string(),
        address: z.string(),
        serviceUuid: Bluetooth.BluetoothUuidSchema,
        characteristicUuid: Bluetooth.BluetoothUuidSchema,
        descriptorUuid: Bluetooth.BluetoothUuidSchema,
        type: z.enum(['read', 'write']),
        data: z.array(z.number().int().nonnegative()).optional(),
    }));
})(Bluetooth || (Bluetooth = {}));
//# sourceMappingURL=webdriver-bidi-bluetooth.js.map