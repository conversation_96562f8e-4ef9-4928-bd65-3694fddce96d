/**
 * Copyright 2024 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * THIS FILE IS AUTOGENERATED by cddlconv 0.1.7.
 * Run `node tools/generate-bidi-types.mjs` to regenerate.
 * @see https://github.com/w3c/webdriver-bidi/blob/master/index.bs
 */
import z from 'zod';
export declare namespace Speculation {
    const PreloadingStatusSchema: z.ZodLazy<z.ZodEnum<["pending", "ready", "success", "failure"]>>;
}
export declare const SpeculationEventSchema: z.Zod<PERSON>azy<z.ZodLazy<z.ZodObject<{
    method: z.ZodLiteral<"speculation.prefetchStatusUpdated">;
    params: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        url: z.ZodString;
        status: z.ZodLazy<z.ZodEnum<["pending", "ready", "success", "failure"]>>;
    }, "strip", z.ZodTypeAny, {
        url: string;
        status: "success" | "pending" | "ready" | "failure";
        context: string;
    }, {
        url: string;
        status: "success" | "pending" | "ready" | "failure";
        context: string;
    }>>;
}, "strip", z.ZodTypeAny, {
    params: {
        url: string;
        status: "success" | "pending" | "ready" | "failure";
        context: string;
    };
    method: "speculation.prefetchStatusUpdated";
}, {
    params: {
        url: string;
        status: "success" | "pending" | "ready" | "failure";
        context: string;
    };
    method: "speculation.prefetchStatusUpdated";
}>>>;
export declare namespace Speculation {
    const PrefetchStatusUpdatedSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"speculation.prefetchStatusUpdated">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            url: z.ZodString;
            status: z.ZodLazy<z.ZodEnum<["pending", "ready", "success", "failure"]>>;
        }, "strip", z.ZodTypeAny, {
            url: string;
            status: "success" | "pending" | "ready" | "failure";
            context: string;
        }, {
            url: string;
            status: "success" | "pending" | "ready" | "failure";
            context: string;
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            url: string;
            status: "success" | "pending" | "ready" | "failure";
            context: string;
        };
        method: "speculation.prefetchStatusUpdated";
    }, {
        params: {
            url: string;
            status: "success" | "pending" | "ready" | "failure";
            context: string;
        };
        method: "speculation.prefetchStatusUpdated";
    }>>;
}
export declare namespace Speculation {
    const PrefetchStatusUpdatedParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        url: z.ZodString;
        status: z.ZodLazy<z.ZodEnum<["pending", "ready", "success", "failure"]>>;
    }, "strip", z.ZodTypeAny, {
        url: string;
        status: "success" | "pending" | "ready" | "failure";
        context: string;
    }, {
        url: string;
        status: "success" | "pending" | "ready" | "failure";
        context: string;
    }>>;
}
