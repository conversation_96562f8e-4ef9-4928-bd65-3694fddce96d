{"version": 3, "file": "EmulationProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/emulation/EmulationProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EACL,wBAAwB,EACxB,6BAA6B,GAC9B,MAAM,oCAAoC,CAAC;AAU5C,MAAM,OAAO,kBAAkB;IAC7B,mBAAmB,CAAqB;IACxC,uBAAuB,CAAyB;IAChD,qBAAqB,CAAuB;IAE5C,YACE,sBAA8C,EAC9C,kBAAsC,EACtC,oBAA0C;QAE1C,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,MAAkD;QAElD,IAAI,aAAa,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,EAAE,CAAC;YACjD,yCAAyC;YACzC,MAAM,IAAI,wBAAwB,CAChC,sDAAsD,CACvD,CAAC;QACJ,CAAC;QAED,IAAI,WAAW,GAGJ,IAAI,CAAC;QAEhB,IAAI,aAAa,IAAI,MAAM,EAAE,CAAC;YAC5B,IACE,CAAC,MAAM,CAAC,WAAW,EAAE,QAAQ,IAAI,IAAI,CAAC,KAAK,IAAI;gBAC/C,CAAC,MAAM,CAAC,WAAW,EAAE,gBAAgB,IAAI,IAAI,CAAC,KAAK,IAAI,EACvD,CAAC;gBACD,MAAM,IAAI,wBAAwB,CAChC,4DAA4D,CAC7D,CAAC;YACJ,CAAC;YAED,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QACnC,CAAC;aAAM,IAAI,OAAO,IAAI,MAAM,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,qBAAqB,EAAE,CAAC;gBAChD,yCAAyC;gBACzC,MAAM,IAAI,wBAAwB,CAChC,6BAA6B,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CACjD,CAAC;YACJ,CAAC;YACD,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,yCAAyC;YACzC,MAAM,IAAI,wBAAwB,CAAC,oCAAoC,CAAC,CAAC;QAC3E,CAAC;QAED,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mCAAmC,CACrE,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,YAAY,CACpB,CAAC;QAEF,KAAK,MAAM,iBAAiB,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,2BAA2B,CACpD,iBAAiB,EACjB;gBACE,WAAW;aACZ,CACF,CAAC;QACJ,CAAC;QACD,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,aAAa,EAAE;gBAChE,WAAW;aACZ,CAAC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CACf,gBAAgB,CAAC,GAAG,CAClB,KAAK,EAAE,OAAO,EAAE,EAAE,CAAC,MAAM,OAAO,CAAC,sBAAsB,CAAC,WAAW,CAAC,CACrE,CACF,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,MAA6C;QAE7C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC;QAErC,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,wBAAwB,CAAC,mBAAmB,MAAM,GAAG,CAAC,CAAC;QACnE,CAAC;QAED,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mCAAmC,CACrE,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,YAAY,CACpB,CAAC;QAEF,KAAK,MAAM,iBAAiB,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,2BAA2B,CACpD,iBAAiB,EACjB;gBACE,MAAM;aACP,CACF,CAAC;QACJ,CAAC;QACD,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,aAAa,EAAE;gBAChE,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CACf,gBAAgB,CAAC,GAAG,CAClB,KAAK,EAAE,OAAO,EAAE,EAAE,CAAC,MAAM,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAC3D,CACF,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,MAA+C;QAE/C,MAAM,gBAAgB,GAAG,MAAM,CAAC,OAAO,CAAC;QAExC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mCAAmC,CACrE,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,YAAY,CACpB,CAAC;QAEF,KAAK,MAAM,iBAAiB,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,2BAA2B,CACpD,iBAAiB,EACjB;gBACE,gBAAgB;aACjB,CACF,CAAC;QACJ,CAAC;QAED,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,aAAa,EAAE;gBAChE,gBAAgB;aACjB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CACf,gBAAgB,CAAC,GAAG,CAClB,KAAK,EAAE,OAAO,EAAE,EAAE,CAAC,MAAM,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CACvE,CACF,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,4BAA4B,CAChC,MAAwD;QAExD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mCAAmC,CACrE,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,YAAY,CACpB,CAAC;QAEF,KAAK,MAAM,iBAAiB,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,2BAA2B,CACpD,iBAAiB,EACjB;gBACE,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;aAC5C,CACF,CAAC;QACJ,CAAC;QACD,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,aAAa,EAAE;gBAChE,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;aAC5C,CAAC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CACf,gBAAgB,CAAC,GAAG,CAClB,KAAK,EAAE,OAAO,EAAE,EAAE,CAChB,MAAM,OAAO,CAAC,4BAA4B,CAAC,MAAM,CAAC,iBAAiB,CAAC,CACvE,CACF,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mCAAmC,CACvC,kBAA6B,EAC7B,cAAyB,EACzB,WAAW,GAAG,KAAK;QAEnB,IAAI,kBAAkB,KAAK,SAAS,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YACrE,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,CAAC;YAC5D,CAAC;YACD,MAAM,IAAI,wBAAwB,CAChC,4DAA4D,CAC7D,CAAC;QACJ,CAAC;QAED,IAAI,kBAAkB,KAAK,SAAS,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YACrE,MAAM,IAAI,wBAAwB,CAChC,4DAA4D,CAC7D,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;YACrC,+BAA+B;YAC/B,IAAI,cAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAI,wBAAwB,CAAC,iCAAiC,CAAC,CAAC;YACxE,CAAC;YAED,uCAAuC;YACvC,MAAM,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,cAAe,CAAC,CAAC;YAExE,KAAK,MAAM,aAAa,IAAI,cAAe,EAAE,CAAC;gBAC5C,MAAM,wBAAwB,GAAG,IAAI,CAAC,uBAAuB;qBAC1D,mBAAmB,EAAE;qBACrB,MAAM,CACL,CAAC,eAAe,EAAE,EAAE,CAAC,eAAe,CAAC,WAAW,KAAK,aAAa,CACnE,CAAC;gBACJ,MAAM,CAAC,IAAI,CAAC,GAAG,wBAAwB,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpC,MAAM,IAAI,wBAAwB,CAChC,qCAAqC,CACtC,CAAC;YACJ,CAAC;YAED,KAAK,MAAM,iBAAiB,IAAI,kBAAkB,EAAE,CAAC;gBACnD,MAAM,eAAe,GACnB,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;gBAC7D,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,EAAE,CAAC;oBACzC,MAAM,IAAI,wBAAwB,CAChC,wDAAwD,CACzD,CAAC;gBACJ,CAAC;gBACD,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QACD,oFAAoF;QACpF,sEAAsE;QACtE,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,MAA+C;QAE/C,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC;QAEvC,IAAI,QAAQ,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,wBAAwB,CAAC,qBAAqB,QAAQ,GAAG,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,QAAQ,KAAK,IAAI,IAAI,sBAAsB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1D,kDAAkD;YAClD,QAAQ,GAAG,MAAM,QAAQ,EAAE,CAAC;QAC9B,CAAC;QAED,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mCAAmC,CACrE,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,YAAY,CACpB,CAAC;QAEF,KAAK,MAAM,iBAAiB,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,2BAA2B,CACpD,iBAAiB,EACjB;gBACE,QAAQ;aACT,CACF,CAAC;QACJ,CAAC;QACD,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,aAAa,EAAE;gBAChE,QAAQ;aACT,CAAC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CACf,gBAAgB,CAAC,GAAG,CAClB,KAAK,EAAE,OAAO,EAAE,EAAE,CAAC,MAAM,OAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAC/D,CACF,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,0BAA0B,CAC9B,MAAgD;QAEhD,IAAI,MAAM,CAAC,SAAS,KAAK,EAAE,EAAE,CAAC;YAC5B,MAAM,IAAI,6BAA6B,CACrC,0CAA0C,CAC3C,CAAC;QACJ,CAAC;QAED,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mCAAmC,CACrE,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,YAAY,EACnB,IAAI,CACL,CAAC;QAEF,KAAK,MAAM,iBAAiB,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,2BAA2B,CACpD,iBAAiB,EACjB;gBACE,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,CACF,CAAC;QACJ,CAAC;QACD,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,aAAa,EAAE;gBAChE,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YACvE,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBAC5C,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CACf,gBAAgB,CAAC,GAAG,CAClB,KAAK,EAAE,OAAO,EAAE,EAAE,CAChB,MAAM,OAAO,CAAC,0BAA0B,CAAC,MAAM,CAAC,SAAS,CAAC,CAC7D,CACF,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;CACF;AAED,sBAAsB;AACtB,MAAM,UAAU,aAAa,CAAC,MAAc;IAC1C,IAAI,CAAC;QACH,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,IAAI,CAAC,YAAY,UAAU,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAC;QACf,CAAC;QACD,wBAAwB;QACxB,MAAM,CAAC,CAAC;IACV,CAAC;AACH,CAAC;AAED,sBAAsB;AACtB,MAAM,UAAU,eAAe,CAAC,QAAgB;IAC9C,IAAI,CAAC;QACH,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAC,QAAQ,EAAE,QAAQ,EAAC,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,IAAI,CAAC,YAAY,UAAU,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAC;QACf,CAAC;QACD,wBAAwB;QACxB,MAAM,CAAC,CAAC;IACV,CAAC;AACH,CAAC;AAED,sBAAsB;AACtB,MAAM,UAAU,sBAAsB,CAAC,QAAgB;IACrD,OAAO,sCAAsC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC/D,CAAC"}