{"version": 3, "file": "Function.js", "sourceRoot": "", "sources": ["../../../../src/util/Function.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAA2C,CAAC;AAE5E;;;;GAIG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,CAC5B,aAAqB,EACc,EAAE;IACrC,IAAI,EAAE,GAAG,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC7C,IAAI,EAAE,EAAE,CAAC;QACP,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,EAAE,GAAG,IAAI,QAAQ,CAAC,UAAU,aAAa,EAAE,CAAC,EAEhC,CAAC;IACb,gBAAgB,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;IACxC,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,EAA+B;IAC/D,IAAI,KAAK,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;IAC1B,IACE,KAAK,CAAC,KAAK,CAAC,2BAA2B,CAAC;QACxC,KAAK,CAAC,KAAK,CAAC,4BAA4B,CAAC,EACzC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,OAAO,GACX,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC;QACrB,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC;QAC1B,KAAK,CAAC,KAAK,CACT,2EAA2E,CAC5E,CAAC;IACJ,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,KAAK,CAAC;IACf,CAAC;IACD,yEAAyE;IACzE,iBAAiB;IACjB,IAAI,MAAM,GAAG,WAAW,CAAC;IACzB,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC/B,MAAM,GAAG,SAAS,MAAM,EAAE,CAAC;QAC3B,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IACD,OAAO,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC;AAC7B,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,CACjC,EAAK,EACL,YAAoC,EACjC,EAAE;IACL,IAAI,KAAK,GAAG,iBAAiB,CAAC,EAAE,CAAC,CAAC;IAClC,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;QAC3D,KAAK,GAAG,KAAK,CAAC,OAAO,CACnB,IAAI,MAAM,CAAC,yBAAyB,IAAI,MAAM,IAAI,WAAW,EAAE,GAAG,CAAC;QACnE,2EAA2E;QAC3E,uEAAuE;QACvE,aAAa;QACb,IAAI,OAAO,GAAG,CACf,CAAC;IACJ,CAAC;IACD,OAAO,cAAc,CAAC,KAAK,CAAiB,CAAC;AAC/C,CAAC,CAAC"}