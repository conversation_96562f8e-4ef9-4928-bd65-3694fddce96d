/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ComplexPSelectorList } from '../injected/PQuerySelector.js';
/**
 * @internal
 */
export declare function parsePSelectors(selector: string): [
    selector: ComplexPSelectorList,
    isPureCSS: boolean,
    hasPseudoClasses: boolean,
    hasAria: boolean
];
//# sourceMappingURL=PSelectorParser.d.ts.map