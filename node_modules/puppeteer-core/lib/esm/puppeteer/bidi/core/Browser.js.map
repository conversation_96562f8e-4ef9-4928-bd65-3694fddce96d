{"version": 3, "file": "Browser.js", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/Browser.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKH,OAAO,EAAC,oBAAoB,EAAC,MAAM,wBAAwB,CAAC;AAC5D,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAC1D,OAAO,EAAC,eAAe,EAAE,eAAe,EAAC,MAAM,0BAA0B,CAAC;AAC1E,OAAO,EAAC,eAAe,EAAE,aAAa,EAAC,MAAM,0BAA0B,CAAC;AAGxE,OAAO,EAAC,iBAAiB,EAAC,MAAM,YAAY,CAAC;AAE7C,OAAO,EAAC,WAAW,EAAC,MAAM,kBAAkB,CAAC;AAY7C;;GAEG;IACU,OAAO;sBAAS,YAAY;;;;;;;;;;iBAA5B,OAAQ,SAAQ,WAgB3B;;;YAgHA,wKAAA,OAAO,6DAIN;YAMD,kKAAM,KAAK,6DAMV;YAMD,mMAAM,gBAAgB,6DAcrB;YAMD,gMAAM,eAAe,6DAIpB;YAMD,4MAAM,mBAAmB,6DAIxB;YAMD,sMAAM,iBAAiB,6DA2CtB;YAMD,mMAAM,gBAAgB,6DAOrB;YAMD,yMAAM,kBAAkB,6DAEvB;;;QA7OD,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAgB;YAChC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,OAAO,CAAC,WAAW,EAAE,CAAC;YAC5B,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAO,IAvBI,mDAAO,EAuBR,KAAK,EAAC;QAChB,OAAO,CAAqB;QACnB,YAAY,GAAG,IAAI,eAAe,EAAE,CAAC;QACrC,aAAa,GAAG,IAAI,GAAG,EAAuB,CAAC;QAC/C,OAAO,CAAU;QACjB,cAAc,GAAG,IAAI,GAAG,EAA6B,CAAC;QAE/D,YAAoB,OAAgB;YAClC,KAAK,EAAE,CAAC;YAER,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACzB,CAAC;QAED,KAAK,CAAC,WAAW;YACf,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAC1C,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAC/B,CAAC;YACF,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE;gBACxC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;YAEH,cAAc,CAAC,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC,EAAE;gBAC9C,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;oBAClC,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,cAAc,CAAC,GAAG,CACrB,IAAI,CAAC,KAAK,EACV,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CACtD,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC/B,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACrC,CAAC;QAED,KAAK,CAAC,iBAAiB;YACrB,MAAM,EACJ,MAAM,EAAE,EAAC,YAAY,EAAC,GACvB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;YAE3D,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;gBACnC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,KAAK,CAAC,qBAAqB;YACzB,0EAA0E;YAC1E,sBAAsB;YACtB,MAAM,UAAU,GAAG,IAAI,GAAG,EAAU,CAAC;YACrC,IAAI,QAAqC,CAAC;YAE1C,CAAC;;;oBACC,MAAM,cAAc,kCAAG,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,QAAA,CAAC;oBACtD,cAAc,CAAC,EAAE,CAAC,gCAAgC,EAAE,IAAI,CAAC,EAAE;wBACzD,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC/B,CAAC,CAAC,CAAC;oBACH,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;oBACxE,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;;;;;;;;;aAC5B;YAED,uDAAuD;YACvD,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;gBAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;oBAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gCAAgC,EAAE,IAAI,CAAC,CAAC;gBAC5D,CAAC;gBACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;QACH,CAAC;QAED,kBAAkB,CAAC,EAAU;YAC3B,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACjD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;YAEpD,MAAM,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAC9C,IAAI,YAAY,CAAC,WAAW,CAAC,CAC9B,CAAC;YACF,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACrC,kBAAkB,CAAC,kBAAkB,EAAE,CAAC;gBAExC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;YAEH,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,IAAI,MAAM;YACR,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QACD,IAAI,kBAAkB;YACpB,mEAAmE;YACnE,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAE,CAAC;QACtD,CAAC;QACD,IAAI,YAAY;YACd,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC;QACpC,CAAC;QACD,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,YAAY,CAAC;QAC3B,CAAC;QACD,IAAI,YAAY;YACd,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QACrC,CAAC;QAGD,OAAO,CAAC,MAAe,EAAE,MAAM,GAAG,KAAK;YACrC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACxB,CAAC;QAMD,KAAK,CAAC,KAAK;YACT,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;YAC/C,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAMD,KAAK,CAAC,gBAAgB,CACpB,mBAA2B,EAC3B,UAAmC,EAAE;YAErC,MAAM,EACJ,MAAM,EAAE,EAAC,MAAM,EAAC,GACjB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACrD,mBAAmB;gBACnB,GAAG,OAAO;gBACV,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE;oBACxC,OAAO,OAAO,CAAC,EAAE,CAAC;gBACpB,CAAC,CAA0B;aAC5B,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC;QAMD,KAAK,CAAC,eAAe,CAAC,SAAiC;YACrD,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACjD,SAAS;aACV,CAAC,CAAC;QACL,CAAC;QAMD,KAAK,CAAC,mBAAmB,CAAC,MAAc;YACtC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACpD,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAMD,KAAK,CAAC,iBAAiB,CACrB,OAA8B;YAE9B,MAAM,WAAW,GACf,OAAO,CAAC,WAAW,KAAK,SAAS;gBAC/B,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC;oBACE,SAAS,EAAE,QAAQ;oBACnB,SAAS,EAAE,OAAO,CAAC,WAAW;oBAC9B,QAAQ,EAAE,OAAO,CAAC,WAAW;oBAC7B,OAAO,EAAE,OAAO,CAAC,eAAe;iBACjC,CAAC;YACR,MAAM,EACJ,MAAM,EAAE,EAAC,WAAW,EAAC,GACtB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACvD,KAAK,EAAE,WAAW;aACnB,CAAC,CAAC;YACH,IAAI,OAAO,CAAC,gBAAgB,EAAE,MAAM,KAAK,cAAc,EAAE,CAAC;gBACxD,MAAM,IAAI,oBAAoB,CAC5B,mDAAmD,CACpD,CAAC;YACJ,CAAC;YACD,IAAI,OAAO,CAAC,gBAAgB,EAAE,MAAM,KAAK,OAAO,EAAE,CAAC;gBACjD,IAAI,OAAO,CAAC,gBAAgB,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;oBACxD,MAAM,IAAI,oBAAoB,CAC5B,yDAAyD,CAC1D,CAAC;gBACJ,CAAC;gBACD,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,6BAA6B,EAAE;oBACrD,gBAAgB,EAAE;wBAChB,IAAI,EAAE,SAAS;wBACf,iBAAiB,EAAE,OAAO,CAAC,gBAAgB,CAAC,YAAY;qBACzD;oBACD,YAAY,EAAE,CAAC,WAAW,CAAC;iBAC5B,CAAC,CAAC;YACL,CAAC;YACD,IAAI,OAAO,CAAC,gBAAgB,EAAE,MAAM,KAAK,MAAM,EAAE,CAAC;gBAChD,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,6BAA6B,EAAE;oBACrD,gBAAgB,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;oBAClC,YAAY,EAAE,CAAC,WAAW,CAAC;iBAC5B,CAAC,CAAC;YACL,CAAC;YACD,OAAO,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAC9C,CAAC;QAMD,KAAK,CAAC,gBAAgB,CAAC,IAAY;YACjC,MAAM,EACJ,MAAM,EAAE,EAAC,SAAS,EAAC,GACpB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAClD,aAAa,EAAE,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAC;aACpC,CAAC,CAAC;YACH,OAAO,SAAS,CAAC;QACnB,CAAC;QAMD,KAAK,CAAC,kBAAkB,CAAC,EAAU;YACjC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAC,SAAS,EAAE,EAAE,EAAC,CAAC,CAAC;QACrE,CAAC;QAEQ,yBAjIR,eAAe,wBAOf,eAAe,CAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,mCASD,eAAe,CAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,kCAiBD,eAAe,CAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,sCAOD,eAAe,CAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,oCAOD,eAAe,CAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,mCA8CD,eAAe,CAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,qCAUD,eAAe,CAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,GAKQ,aAAa,EAAC;YACtB,IAAI,CAAC,OAAO;gBACV,+DAA+D,CAAC;YAClE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;YAC9C,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;YAElD,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;QACzB,CAAC;;;SA1QU,OAAO"}