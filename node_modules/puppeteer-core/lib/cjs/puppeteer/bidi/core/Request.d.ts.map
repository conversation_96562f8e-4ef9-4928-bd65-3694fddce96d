{"version": 3, "file": "Request.d.ts", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/Request.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,IAAI,MAAM,yBAAyB,CAAC;AAGhD,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAE1D,OAAO,EAAkB,aAAa,EAAC,MAAM,0BAA0B,CAAC;AAGxE,OAAO,KAAK,EAAC,eAAe,EAAC,MAAM,sBAAsB,CAAC;AAE1D;;GAEG;AACH,qBAAa,OAAQ,SAAQ,YAAY,CAAC;IACxC,8CAA8C;IAC9C,QAAQ,EAAE,OAAO,CAAC;IAClB,yCAAyC;IACzC,YAAY,EAAE,IAAI,CAAC;IACnB,yCAAyC;IACzC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;IACnC,sCAAsC;IACtC,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;;IACA,MAAM,CAAC,IAAI,CACT,eAAe,EAAE,eAAe,EAChC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,2BAA2B,GAC9C,OAAO;IAcV,OAAO;IAgGP,IAAI,QAAQ,IAAI,OAAO,CAEtB;IACD,IAAI,KAAK,IAAI,MAAM,GAAG,SAAS,CAE9B;IACD,IAAI,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAEnC;IACD,IAAI,EAAE,IAAI,MAAM,CAEf;IACD,IAAI,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAElD;IACD,IAAI,MAAM,IAAI,MAAM,CAEnB;IACD,IAAI,UAAU,IAAI,MAAM,GAAG,SAAS,CAEnC;IACD,IAAI,QAAQ,IAAI,OAAO,GAAG,SAAS,CAElC;IACD,IAAI,YAAY,IAAI,OAAO,GAAG,SAAS,CAStC;IACD,IAAI,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,SAAS,CAEpD;IACD,IAAI,GAAG,IAAI,MAAM,CAEhB;IACD,IAAI,SAAS,IAAI,OAAO,CAEvB;IAED,IAAI,YAAY,IAAI,MAAM,GAAG,SAAS,CAGrC;IAED,IAAI,QAAQ,IAAI,MAAM,GAAG,SAAS,CAGjC;IAED,IAAI,WAAW,IAAI,OAAO,CAGzB;IAEK,eAAe,CAAC,EACpB,GAAG,EACH,MAAM,EACN,OAAO,EACP,OAAO,EACP,IAAI,GACL,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,SAAS,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAWpE,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC;IAM5B,eAAe,CAAC,EACpB,UAAU,EACV,YAAY,EACZ,OAAO,EACP,IAAI,GACL,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,SAAS,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAUpE,kBAAkB,IAAI,OAAO,CAAC,UAAU,CAAC;IAgCzC,gBAAgB,CACpB,UAAU,EACN,IAAI,CAAC,OAAO,CAAC,2BAA2B,GACxC,IAAI,CAAC,OAAO,CAAC,6BAA6B,GAC7C,OAAO,CAAC,IAAI,CAAC;IAgBhB,OAAO,CAAC,OAAO;IAIN,CAAC,aAAa,CAAC,IAAI,IAAI;IAKhC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe;CAGvC"}