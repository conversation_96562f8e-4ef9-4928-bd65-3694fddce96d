{"version": 3, "file": "Connection.d.ts", "sourceRoot": "", "sources": ["../../../../src/bidi/Connection.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,KAAK,YAAY,MAAM,4CAA4C,CAAC;AAIhF,OAAO,KAAK,EAAC,mBAAmB,EAAC,MAAM,kCAAkC,CAAC;AAG1E,OAAO,KAAK,EAAC,kBAAkB,EAAC,MAAM,2BAA2B,CAAC;AAClE,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AAIvD,OAAO,KAAK,EACV,UAAU,EACV,QAAQ,IAAI,YAAY,EACxB,UAAU,EACX,MAAM,sBAAsB,CAAC;AAK9B,MAAM,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC;AAE9C;;GAEG;AACH,MAAM,WAAW,QAAS,SAAQ,YAAY;IAC5C,sBAAsB,EAAE;QACtB,MAAM,EAAE,YAAY,CAAC,GAAG,CAAC,qBAAqB,CAAC;QAC/C,UAAU,EAAE,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC;KAChD,CAAC;IACF,qBAAqB,EAAE;QACrB,MAAM,EAAE,YAAY,CAAC,GAAG,CAAC,oBAAoB,CAAC;QAC9C,UAAU,EAAE,YAAY,CAAC,GAAG,CAAC,gBAAgB,CAAC;KAC/C,CAAC;IACF,uBAAuB,EAAE;QACvB,MAAM,EAAE,YAAY,CAAC,GAAG,CAAC,sBAAsB,CAAC;QAChD,UAAU,EAAE,YAAY,CAAC,GAAG,CAAC,kBAAkB,CAAC;KACjD,CAAC;CACH;AAED;;GAEG;AACH,qBAAa,cACX,SAAQ,YAAY,CAAC,UAAU,CAC/B,YAAW,UAAU;;gBAWnB,GAAG,EAAE,MAAM,EACX,SAAS,EAAE,mBAAmB,EAC9B,KAAK,SAAI,EACT,OAAO,CAAC,EAAE,MAAM;IAYlB,IAAI,MAAM,IAAI,OAAO,CAEpB;IAED,IAAI,GAAG,IAAI,MAAM,CAEhB;IAED,MAAM,CAAC,MAAM,SAAS,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI;IAI7D,IAAI,CAAC,GAAG,SAAS,MAAM,kBAAkB,CAAC,UAAU,CAAC,EAC5D,IAAI,EAAE,GAAG,EACT,KAAK,EAAE,kBAAkB,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,GACzC,OAAO;IAOV,IAAI,CAAC,CAAC,SAAS,MAAM,QAAQ,EAC3B,MAAM,EAAE,CAAC,EACT,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAC7B,OAAO,CAAC,EAAE,MAAM,GACf,OAAO,CAAC;QAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAA;KAAC,CAAC;IAe/C;;OAEG;cACa,SAAS,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA+CzD;;;;OAIG;IACH,MAAM,IAAI,IAAI;IAYd;;OAEG;IACH,OAAO,IAAI,IAAI;IAKf,wBAAwB,IAAI,KAAK,EAAE;CAGpC"}