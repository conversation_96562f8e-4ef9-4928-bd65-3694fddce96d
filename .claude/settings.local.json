{"permissions": {"allow": ["WebSearch", "WebFetch(domain:cs.android.com)", "<PERSON><PERSON>(git mv:*)", "Bash(awk:*)", "Bash(sort:*)", "Bash(git add:*)", "Bash(git commit:*)", "WebFetch(domain:bbs.kanxue.com)", "Bash(__NEW_LINE__ sed -i.bak '\ns|//add FART hook|// Interpreter profiling hook|g\n' /Users/<USER>/AndroidStudioProjects/FART/art/runtime/interpreter/interpreter.cc)", "Bash(__NEW_LINE__ echo \"All FART keywords replaced\")", "Bash(art/runtime/art_method.cc )", "Bash(art/runtime/interpreter/interpreter.cc )", "Bash(art/runtime/native/dalvik_system_DexFile.cc )", "Bash(frameworks/base/core/java/android/app/ActivityThread.java )", "Bash(frameworks/base/core/java/android/util/AppProfiler.java )", "Bash(libcore/dalvik/src/main/java/dalvik/system/DexFile.java)"], "deny": [], "ask": []}}